<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; fill: #34495e; }
      .box-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: white; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #ecf0f1; }
      .code-text { font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace; font-size: 12px; fill: white; }
      .start-box { fill: #27ae60; stroke: #229954; stroke-width: 1.5; }
      .input-box { fill: #4a90e2; stroke: #357abd; stroke-width: 1.5; }
      .calc-box { fill: #d73527; stroke: #b8251a; stroke-width: 1.5; }
      .decision-box { fill: #f5a623; stroke: #d4941e; stroke-width: 1.5; }
      .process-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 1.5; }
      .output-box { fill: #1abc9c; stroke: #16a085; stroke-width: 1.5; }
      .arrow { stroke: #495057; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">MT图解法代码逻辑流程图</text>
  <text x="600" y="55" text-anchor="middle" class="subtitle">McCabe-Thiele Method Programming Logic</text>

  <!-- 开始 -->
  <rect x="500" y="80" width="200" height="60" rx="10" class="start-box"/>
  <text x="600" y="105" text-anchor="middle" class="box-text-white">程序启动</text>
  <text x="600" y="125" text-anchor="middle" class="small-text-white">Initialize Program</text>

  <!-- 参数输入 -->
  <rect x="500" y="170" width="200" height="80" rx="10" class="input-box"/>
  <text x="600" y="195" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="600" y="215" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>
  <text x="600" y="235" text-anchor="middle" class="code-text">GUI.get_parameters()</text>

  <!-- 参数验证 -->
  <polygon points="600,280 650,310 600,340 550,310" class="decision-box"/>
  <text x="600" y="305" text-anchor="middle" class="box-text-white">参数验证</text>
  <text x="600" y="320" text-anchor="middle" class="small-text-white">Valid?</text>

  <!-- 错误处理 -->
  <rect x="750" y="280" width="150" height="60" rx="10" class="input-box"/>
  <text x="825" y="305" text-anchor="middle" class="box-text-white">错误提示</text>
  <text x="825" y="325" text-anchor="middle" class="small-text-white">Error Message</text>

  <!-- VLE数据计算 -->
  <rect x="30" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="130" y="405" text-anchor="middle" class="box-text-white">VLE数据计算</text>
  <text x="130" y="425" text-anchor="middle" class="small-text-white">y = αx/(1+(α-1)x)</text>
  <text x="130" y="445" text-anchor="middle" class="code-text">calc_vle_curve()</text>

  <!-- 操作线方程 -->
  <rect x="250" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="350" y="405" text-anchor="middle" class="box-text-white">操作线方程</text>
  <text x="350" y="425" text-anchor="middle" class="small-text-white">ROL and SOL</text>
  <text x="350" y="445" text-anchor="middle" class="code-text">calc_operating_lines()</text>

  <!-- q线计算 -->
  <rect x="470" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="570" y="405" text-anchor="middle" class="box-text-white">q线计算</text>
  <text x="570" y="425" text-anchor="middle" class="small-text-white">q-line equation</text>
  <text x="570" y="445" text-anchor="middle" class="code-text">calc_q_line()</text>

  <!-- 交点计算 -->
  <rect x="690" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="790" y="405" text-anchor="middle" class="box-text-white">交点计算</text>
  <text x="790" y="425" text-anchor="middle" class="small-text-white">ROL ∩ q-line</text>
  <text x="790" y="445" text-anchor="middle" class="code-text">find_intersection()</text>

  <!-- 加料板位置 -->
  <rect x="910" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="1010" y="405" text-anchor="middle" class="box-text-white">加料板位置</text>
  <text x="1010" y="425" text-anchor="middle" class="small-text-white">Feed Stage</text>
  <text x="1010" y="445" text-anchor="middle" class="code-text">determine_feed_stage()</text>

  <!-- 逐板计算初始化 -->
  <rect x="500" y="520" width="200" height="80" rx="10" class="process-box"/>
  <text x="600" y="545" text-anchor="middle" class="box-text-white">逐板计算初始化</text>
  <text x="600" y="565" text-anchor="middle" class="small-text-white">n=1, x=xD, y=xD</text>
  <text x="600" y="585" text-anchor="middle" class="code-text">initialize_stepping()</text>

  <!-- 精馏段计算 -->
  <rect x="280" y="650" width="200" height="80" rx="10" class="process-box"/>
  <text x="380" y="675" text-anchor="middle" class="box-text-white">精馏段计算</text>
  <text x="380" y="695" text-anchor="middle" class="small-text-white">ROL: y=f(x)</text>
  <text x="380" y="715" text-anchor="middle" class="code-text">rectifying_section()</text>

  <!-- 判断是否到达加料板 -->
  <polygon points="600,650 660,680 600,710 540,680" class="decision-box"/>
  <text x="600" y="675" text-anchor="middle" class="box-text-white">到达加料板?</text>
  <text x="600" y="690" text-anchor="middle" class="small-text-white">Feed Stage?</text>

  <!-- 提馏段计算 -->
  <rect x="720" y="650" width="200" height="80" rx="10" class="process-box"/>
  <text x="820" y="675" text-anchor="middle" class="box-text-white">提馏段计算</text>
  <text x="820" y="695" text-anchor="middle" class="small-text-white">SOL: y=f(x)</text>
  <text x="820" y="715" text-anchor="middle" class="code-text">stripping_section()</text>

  <!-- VLE平衡计算 -->
  <rect x="500" y="780" width="200" height="80" rx="10" class="process-box"/>
  <text x="600" y="805" text-anchor="middle" class="box-text-white">VLE平衡计算</text>
  <text x="600" y="825" text-anchor="middle" class="small-text-white">x = f(y)</text>
  <text x="600" y="845" text-anchor="middle" class="code-text">equilibrium_calc()</text>

  <!-- 收敛判断 -->
  <polygon points="600,910 660,940 600,970 540,940" class="decision-box"/>
  <text x="600" y="935" text-anchor="middle" class="box-text-white">x ≤ xW?</text>
  <text x="600" y="950" text-anchor="middle" class="small-text-white">收敛判断</text>

  <!-- 板数递增 -->
  <rect x="320" y="910" width="180" height="60" rx="10" class="process-box"/>
  <text x="410" y="935" text-anchor="middle" class="box-text-white">n = n + 1</text>
  <text x="410" y="955" text-anchor="middle" class="code-text">increment_stage()</text>

  <!-- 结果输出 -->
  <rect x="450" y="1020" width="180" height="60" rx="10" class="output-box"/>
  <text x="540" y="1045" text-anchor="middle" class="box-text-white">结果输出</text>
  <text x="540" y="1065" text-anchor="middle" class="small-text-white">总板数 = n</text>

  <!-- MT图绘制 -->
  <rect x="650" y="1020" width="180" height="60" rx="10" class="output-box"/>
  <text x="740" y="1045" text-anchor="middle" class="box-text-white">MT图绘制</text>
  <text x="740" y="1065" text-anchor="middle" class="small-text-white">阶梯图显示</text>

  <!-- 连接线 -->
  <line x1="600" y1="140" x2="600" y2="170" class="arrow"/>
  <line x1="600" y1="250" x2="600" y2="280" class="arrow"/>
  <line x1="650" y1="310" x2="750" y2="310" class="arrow"/>
  <line x1="825" y1="280" x2="825" y2="210" class="arrow"/>
  <line x1="825" y1="210" x2="700" y2="210" class="arrow"/>
  <line x1="600" y1="340" x2="600" y2="380" class="arrow"/>

  <!-- 分散到各计算模块 -->
  <line x1="600" y1="380" x2="130" y2="380" class="arrow"/>
  <line x1="600" y1="380" x2="350" y2="380" class="arrow"/>
  <line x1="600" y1="380" x2="570" y2="380" class="arrow"/>
  <line x1="600" y1="380" x2="790" y2="380" class="arrow"/>
  <line x1="600" y1="380" x2="1010" y2="380" class="arrow"/>

  <!-- 汇聚到初始化 -->
  <line x1="130" y1="460" x2="540" y2="520" class="arrow"/>
  <line x1="350" y1="460" x2="570" y2="520" class="arrow"/>
  <line x1="570" y1="460" x2="600" y2="520" class="arrow"/>
  <line x1="790" y1="460" x2="630" y2="520" class="arrow"/>
  <line x1="1010" y1="460" x2="660" y2="520" class="arrow"/>

  <line x1="600" y1="600" x2="600" y2="650" class="arrow"/>
  <line x1="540" y1="680" x2="480" y2="680" class="arrow"/>
  <line x1="660" y1="680" x2="720" y2="680" class="arrow"/>

  <!-- 汇聚到平衡计算 -->
  <line x1="380" y1="730" x2="550" y2="780" class="arrow"/>
  <line x1="820" y1="730" x2="650" y2="780" class="arrow"/>

  <line x1="600" y1="860" x2="600" y2="910" class="arrow"/>
  <line x1="540" y1="940" x2="500" y2="940" class="arrow"/>
  <line x1="410" y1="910" x2="410" y2="680" class="arrow"/>
  <line x1="410" y1="680" x2="540" y2="680" class="arrow"/>

  <line x1="600" y1="970" x2="600" y2="1000" class="arrow"/>
  <line x1="600" y1="1000" x2="540" y2="1020" class="arrow"/>
  <line x1="600" y1="1000" x2="740" y2="1020" class="arrow"/>

  <!-- 标注 -->
  <text x="700" y="305" text-anchor="middle" class="small-text">否</text>
  <text x="620" y="360" text-anchor="start" class="small-text">是</text>
  <text x="510" y="675" text-anchor="middle" class="small-text">否</text>
  <text x="690" y="675" text-anchor="middle" class="small-text">是</text>
  <text x="510" y="935" text-anchor="middle" class="small-text">否</text>
  <text x="620" y="995" text-anchor="start" class="small-text">是</text>

</svg>
