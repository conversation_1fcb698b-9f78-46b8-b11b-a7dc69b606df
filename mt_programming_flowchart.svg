<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; fill: #34495e; }
      .box-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: #2c3e50; }
      .box-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: white; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 14px; fill: #ecf0f1; }
      .input-box { fill: #4a90e2; stroke: #357abd; stroke-width: 1.5; }
      .core-box { fill: #d73527; stroke: #b8251a; stroke-width: 1.5; }
      .gui-box { fill: #f5a623; stroke: #d4941e; stroke-width: 1.5; }
      .visual-box { fill: #7ed321; stroke: #6bb518; stroke-width: 1.5; }
      .debug-box { fill: #9013fe; stroke: #7b1fa2; stroke-width: 1.5; }
      .output-box { fill: #50e3c2; stroke: #4dd0e1; stroke-width: 1.5; }
      .advanced-box { fill: #bd10e0; stroke: #9c27b0; stroke-width: 1.5; }
      .arrow { stroke: #495057; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">MT图解法编程实践流程图</text>
  <text x="600" y="55" text-anchor="middle" class="subtitle">基于GAI辅助的化工计算程序开发</text>
  
  <!-- (1) 参数输入与物系处理 -->
  <rect x="50" y="90" width="280" height="100" rx="10" class="input-box"/>
  <text x="190" y="115" text-anchor="middle" class="box-text-white">(1) 参数输入与物系处理</text>
  <text x="190" y="135" text-anchor="middle" class="small-text-white">GUI界面设计：xD, xW, xF, q, R</text>
  <text x="190" y="155" text-anchor="middle" class="small-text-white">物系处理：苯-甲苯(α=2.5)</text>
  <text x="190" y="175" text-anchor="middle" class="small-text-white">扩展：乙醇-水VLE数据</text>
  
  <!-- (2) 核心计算逻辑实现 -->
  <rect x="380" y="90" width="280" height="100" rx="10" class="core-box"/>
  <text x="520" y="115" text-anchor="middle" class="box-text-white">(2) 核心计算逻辑实现</text>
  <text x="520" y="135" text-anchor="middle" class="small-text-white">VLE线、ROL、SOL、q线方程</text>
  <text x="520" y="155" text-anchor="middle" class="small-text-white">逐板计算迭代过程</text>
  <text x="520" y="175" text-anchor="middle" class="small-text-white">模块化设计与GAI协助</text>
  
  <!-- (3) GUI交互界面开发 -->
  <rect x="710" y="90" width="280" height="100" rx="10" class="gui-box"/>
  <text x="850" y="115" text-anchor="middle" class="box-text-white">(3) GUI交互界面开发</text>
  <text x="850" y="135" text-anchor="middle" class="small-text-white">Tkinter界面设计</text>
  <text x="850" y="155" text-anchor="middle" class="small-text-white">控件布局与交互逻辑</text>
  <text x="850" y="175" text-anchor="middle" class="small-text-white">GAI生成框架代码</text>
  
  <!-- 箭头连接第一行 -->
  <line x1="330" y1="140" x2="380" y2="140" class="arrow"/>
  <line x1="660" y1="140" x2="710" y2="140" class="arrow"/>
  
  <!-- (4) 动态可视化与阶梯绘制 -->
  <rect x="50" y="240" width="280" height="100" rx="10" class="visual-box"/>
  <text x="190" y="265" text-anchor="middle" class="box-text-white">(4) 动态可视化与阶梯绘制</text>
  <text x="190" y="285" text-anchor="middle" class="small-text-white">MT图动态绘制</text>
  <text x="190" y="305" text-anchor="middle" class="small-text-white">理论板数阶梯呈现</text>
  <text x="190" y="325" text-anchor="middle" class="small-text-white">加料板位置判断</text>
  
  <!-- (5) 代码调试与优化 -->
  <rect x="380" y="240" width="280" height="100" rx="10" class="debug-box"/>
  <text x="520" y="265" text-anchor="middle" class="box-text-white">(5) 代码调试与优化</text>
  <text x="520" y="285" text-anchor="middle" class="small-text-white">GAI智能调试器</text>
  <text x="520" y="305" text-anchor="middle" class="small-text-white">错误定位与修复建议</text>
  <text x="520" y="325" text-anchor="middle" class="small-text-white">代码结构优化</text>
  
  <!-- (6) 结果展示 -->
  <rect x="710" y="240" width="280" height="100" rx="10" class="output-box"/>
  <text x="850" y="265" text-anchor="middle" class="box-text-white">(6) 结果展示</text>
  <text x="850" y="285" text-anchor="middle" class="small-text-white">总理论板数计算</text>
  <text x="850" y="305" text-anchor="middle" class="small-text-white">精馏段/提馏段板数</text>
  <text x="850" y="325" text-anchor="middle" class="small-text-white">最佳加料板位置</text>
  
  <!-- 箭头连接第二行 -->
  <line x1="330" y1="290" x2="380" y2="290" class="arrow"/>
  <line x1="660" y1="290" x2="710" y2="290" class="arrow"/>
  
  <!-- 垂直箭头连接 -->
  <line x1="190" y1="190" x2="190" y2="240" class="arrow"/>
  <line x1="520" y1="190" x2="520" y2="240" class="arrow"/>
  <line x1="850" y1="190" x2="850" y2="240" class="arrow"/>
  
  <!-- (7) 进阶功能探索 -->
  <rect x="380" y="390" width="280" height="100" rx="10" class="advanced-box"/>
  <text x="520" y="415" text-anchor="middle" class="box-text-white">(7) 进阶功能探索</text>
  <text x="520" y="435" text-anchor="middle" class="small-text-white">外部VLE数据读取</text>
  <text x="520" y="455" text-anchor="middle" class="small-text-white">物性转换模块</text>
  <text x="520" y="475" text-anchor="middle" class="small-text-white">最小回流比计算</text>
  
  <!-- 汇聚到进阶功能 -->
  <line x1="190" y1="340" x2="470" y2="390" class="arrow"/>
  <line x1="520" y1="340" x2="520" y2="390" class="arrow"/>
  <line x1="850" y1="340" x2="570" y2="390" class="arrow"/>
  
  <!-- GAI辅助说明框 -->
  <rect x="50" y="540" width="900" height="120" rx="10" fill="#f8f9fa" stroke="#adb5bd" stroke-width="1.5"/>
  <text x="500" y="565" text-anchor="middle" class="subtitle">GAI辅助关键技术点</text>
  <text x="70" y="590" class="small-text">• 模块化设计指导：VLE数据处理、操作线方程计算、逐板计算算法、q线处理模块</text>
  <text x="70" y="610" class="small-text">• 代码生成示例："请基于相对挥发度α和液相组成x，编写Python函数计算平衡气相组成y"</text>
  <text x="70" y="630" class="small-text">• 调试支持：浮点数精度问题、逻辑判断边界条件、绘图错误分析与修正</text>
  <text x="70" y="650" class="small-text">• 界面开发：Tkinter控件布局、参数输入框、MT图显示区域、结果输出文本框</text>
  
  <!-- 技术难点说明框 -->
  <rect x="50" y="690" width="900" height="120" rx="10" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1.5"/>
  <text x="500" y="715" text-anchor="middle" class="subtitle">核心技术难点</text>
  <text x="70" y="740" class="small-text">• q线附近操作线切换点判断：加料板位置的精确确定</text>
  <text x="70" y="760" class="small-text">• 阶梯与平衡线、操作线交点的精确计算：几何作图规则的编程实现</text>
  <text x="70" y="780" class="small-text">• 动态可视化：MT图的实时绘制与理论板数阶梯的准确呈现</text>
  <text x="70" y="800" class="small-text">• 数据处理：外部VLE数据读取、插值方法应用、物性转换算法</text>
  
  <!-- 流程连接线说明 -->
  <text x="1050" y="140" text-anchor="start" class="small-text">并行开发</text>
  <text x="1050" y="290" text-anchor="start" class="small-text">迭代优化</text>
  <text x="1050" y="440" text-anchor="start" class="small-text">功能扩展</text>
  
</svg>
