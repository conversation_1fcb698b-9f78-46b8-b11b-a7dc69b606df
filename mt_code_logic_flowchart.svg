<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; fill: #34495e; }
      .box-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: #2c3e50; }
      .box-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: white; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 14px; fill: #ecf0f1; }
      .code-text { font-family: 'Consolas', 'Monaco', monospace; font-size: 12px; fill: #2c3e50; }
      .start-box { fill: #27ae60; stroke: #229954; stroke-width: 1.5; }
      .input-box { fill: #4a90e2; stroke: #357abd; stroke-width: 1.5; }
      .calc-box { fill: #d73527; stroke: #b8251a; stroke-width: 1.5; }
      .decision-box { fill: #f5a623; stroke: #d4941e; stroke-width: 1.5; }
      .process-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 1.5; }
      .output-box { fill: #1abc9c; stroke: #16a085; stroke-width: 1.5; }
      .arrow { stroke: #495057; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">MT图解法代码逻辑流程图</text>
  <text x="700" y="55" text-anchor="middle" class="subtitle">McCabe-Thiele Method Programming Logic</text>
  
  <!-- 开始 -->
  <rect x="600" y="80" width="200" height="60" rx="10" class="start-box"/>
  <text x="700" y="105" text-anchor="middle" class="box-text-white">程序启动</text>
  <text x="700" y="125" text-anchor="middle" class="small-text-white">Initialize Program</text>
  
  <!-- 箭头1 -->
  <line x1="700" y1="140" x2="700" y2="170" class="arrow"/>
  
  <!-- 参数输入 -->
  <rect x="600" y="170" width="200" height="80" rx="10" class="input-box"/>
  <text x="700" y="195" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="700" y="215" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>
  <text x="700" y="235" text-anchor="middle" class="code-text" fill="white">GUI.get_parameters()</text>
  
  <!-- 箭头2 -->
  <line x1="700" y1="250" x2="700" y2="280" class="arrow"/>
  
  <!-- 参数验证 -->
  <polygon points="700,280 750,310 700,340 650,310" class="decision-box"/>
  <text x="700" y="305" text-anchor="middle" class="box-text-white">参数验证</text>
  <text x="700" y="320" text-anchor="middle" class="small-text-white">Valid?</text>
  
  <!-- 错误处理 -->
  <rect x="850" y="280" width="180" height="60" rx="10" class="input-box"/>
  <text x="940" y="305" text-anchor="middle" class="box-text-white">错误提示</text>
  <text x="940" y="325" text-anchor="middle" class="small-text-white">Error Message</text>
  
  <!-- 箭头：否 -->
  <line x1="750" y1="310" x2="850" y2="310" class="arrow"/>
  <text x="800" y="305" text-anchor="middle" class="small-text">否</text>
  
  <!-- 箭头：返回输入 -->
  <line x1="940" y1="280" x2="940" y2="210" class="arrow"/>
  <line x1="940" y1="210" x2="800" y2="210" class="arrow"/>
  
  <!-- 箭头：是 -->
  <line x1="700" y1="340" x2="700" y2="380" class="arrow"/>
  <text x="720" y="360" text-anchor="start" class="small-text">是</text>
  
  <!-- VLE数据计算 -->
  <rect x="50" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="150" y="405" text-anchor="middle" class="box-text-white">VLE数据计算</text>
  <text x="150" y="425" text-anchor="middle" class="small-text-white">y = αx/(1+(α-1)x)</text>
  <text x="150" y="445" text-anchor="middle" class="code-text" fill="white">calc_vle_curve()</text>
  
  <!-- 操作线方程 -->
  <rect x="300" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="400" y="405" text-anchor="middle" class="box-text-white">操作线方程</text>
  <text x="400" y="425" text-anchor="middle" class="small-text-white">ROL & SOL</text>
  <text x="400" y="445" text-anchor="middle" class="code-text" fill="white">calc_operating_lines()</text>
  
  <!-- q线计算 -->
  <rect x="550" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="650" y="405" text-anchor="middle" class="box-text-white">q线计算</text>
  <text x="650" y="425" text-anchor="middle" class="small-text-white">q-line equation</text>
  <text x="650" y="445" text-anchor="middle" class="code-text" fill="white">calc_q_line()</text>
  
  <!-- 交点计算 -->
  <rect x="800" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="900" y="405" text-anchor="middle" class="box-text-white">交点计算</text>
  <text x="900" y="425" text-anchor="middle" class="small-text-white">ROL∩q-line</text>
  <text x="900" y="445" text-anchor="middle" class="code-text" fill="white">find_intersection()</text>
  
  <!-- 加料板位置 -->
  <rect x="1050" y="380" width="200" height="80" rx="10" class="calc-box"/>
  <text x="1150" y="405" text-anchor="middle" class="box-text-white">加料板位置</text>
  <text x="1150" y="425" text-anchor="middle" class="small-text-white">Feed Stage</text>
  <text x="1150" y="445" text-anchor="middle" class="code-text" fill="white">determine_feed_stage()</text>
  
  <!-- 连接到计算模块 -->
  <line x1="700" y1="380" x2="150" y2="380" class="arrow"/>
  <line x1="700" y1="380" x2="400" y2="380" class="arrow"/>
  <line x1="700" y1="380" x2="650" y2="380" class="arrow"/>
  <line x1="700" y1="380" x2="900" y2="380" class="arrow"/>
  <line x1="700" y1="380" x2="1150" y2="380" class="arrow"/>
  
  <!-- 逐板计算初始化 -->
  <rect x="600" y="520" width="200" height="80" rx="10" class="process-box"/>
  <text x="700" y="545" text-anchor="middle" class="box-text-white">逐板计算初始化</text>
  <text x="700" y="565" text-anchor="middle" class="small-text-white">n=1, x=xD, y=xD</text>
  <text x="700" y="585" text-anchor="middle" class="code-text" fill="white">initialize_stepping()</text>
  
  <!-- 汇聚箭头 -->
  <line x1="150" y1="460" x2="650" y2="520" class="arrow"/>
  <line x1="400" y1="460" x2="675" y2="520" class="arrow"/>
  <line x1="650" y1="460" x2="700" y2="520" class="arrow"/>
  <line x1="900" y1="460" x2="725" y2="520" class="arrow"/>
  <line x1="1150" y1="460" x2="750" y2="520" class="arrow"/>
  
  <!-- 精馏段计算循环 -->
  <rect x="300" y="650" width="200" height="80" rx="10" class="process-box"/>
  <text x="400" y="675" text-anchor="middle" class="box-text-white">精馏段计算</text>
  <text x="400" y="695" text-anchor="middle" class="small-text-white">ROL: y=f(x)</text>
  <text x="400" y="715" text-anchor="middle" class="code-text" fill="white">rectifying_section()</text>
  
  <!-- 判断是否到达加料板 -->
  <polygon points="700,650 750,680 700,710 650,680" class="decision-box"/>
  <text x="700" y="675" text-anchor="middle" class="box-text-white">到达</text>
  <text x="700" y="690" text-anchor="middle" class="box-text-white">加料板?</text>
  
  <!-- 提馏段计算 -->
  <rect x="900" y="650" width="200" height="80" rx="10" class="process-box"/>
  <text x="1000" y="675" text-anchor="middle" class="box-text-white">提馏段计算</text>
  <text x="1000" y="695" text-anchor="middle" class="small-text-white">SOL: y=f(x)</text>
  <text x="1000" y="715" text-anchor="middle" class="code-text" fill="white">stripping_section()</text>
  
  <!-- 箭头连接 -->
  <line x1="700" y1="600" x2="700" y2="650" class="arrow"/>
  <line x1="650" y1="680" x2="500" y2="680" class="arrow"/>
  <text x="575" y="675" text-anchor="middle" class="small-text">否</text>
  <line x1="750" y1="680" x2="900" y2="680" class="arrow"/>
  <text x="825" y="675" text-anchor="middle" class="small-text">是</text>
  
  <!-- VLE平衡计算 -->
  <rect x="600" y="780" width="200" height="80" rx="10" class="process-box"/>
  <text x="700" y="805" text-anchor="middle" class="box-text-white">VLE平衡计算</text>
  <text x="700" y="825" text-anchor="middle" class="small-text-white">x = f(y)</text>
  <text x="700" y="845" text-anchor="middle" class="code-text" fill="white">equilibrium_calc()</text>
  
  <!-- 汇聚到平衡计算 -->
  <line x1="400" y1="730" x2="650" y2="780" class="arrow"/>
  <line x1="1000" y1="730" x2="750" y2="780" class="arrow"/>
  
  <!-- 收敛判断 -->
  <polygon points="700,910 750,940 700,970 650,940" class="decision-box"/>
  <text x="700" y="935" text-anchor="middle" class="box-text-white">x ≤ xW?</text>
  <text x="700" y="950" text-anchor="middle" class="small-text-white">收敛判断</text>
  
  <!-- 箭头连接 -->
  <line x1="700" y1="860" x2="700" y2="910" class="arrow"/>
  
  <!-- 板数递增 -->
  <rect x="450" y="910" width="150" height="60" rx="10" class="process-box"/>
  <text x="525" y="935" text-anchor="middle" class="box-text-white">n = n + 1</text>
  <text x="525" y="955" text-anchor="middle" class="code-text" fill="white">increment_stage()</text>
  
  <!-- 循环箭头 -->
  <line x1="650" y1="940" x2="600" y2="940" class="arrow"/>
  <text x="625" y="935" text-anchor="middle" class="small-text">否</text>
  <line x1="525" y1="910" x2="525" y2="680" class="arrow"/>
  <line x1="525" y1="680" x2="650" y2="680" class="arrow"/>
  
  <!-- 结果输出 -->
  <rect x="600" y="1020" width="200" height="80" rx="10" class="output-box"/>
  <text x="700" y="1045" text-anchor="middle" class="box-text-white">结果输出</text>
  <text x="700" y="1065" text-anchor="middle" class="small-text-white">总板数 = n</text>
  <text x="700" y="1085" text-anchor="middle" class="code-text" fill="white">display_results()</text>
  
  <!-- 最终箭头 -->
  <line x1="700" y1="970" x2="700" y2="1020" class="arrow"/>
  <text x="720" y="995" text-anchor="start" class="small-text">是</text>
  
  <!-- MT图绘制 -->
  <rect x="850" y="1020" width="200" height="80" rx="10" class="output-box"/>
  <text x="950" y="1045" text-anchor="middle" class="box-text-white">MT图绘制</text>
  <text x="950" y="1065" text-anchor="middle" class="small-text-white">阶梯图显示</text>
  <text x="950" y="1085" text-anchor="middle" class="code-text" fill="white">plot_mt_diagram()</text>
  
  <!-- 连接到绘图 -->
  <line x1="800" y1="1060" x2="850" y2="1060" class="arrow"/>
  
  <!-- 代码模块说明 -->
  <rect x="50" y="1130" width="1300" height="60" rx="10" fill="#f8f9fa" stroke="#adb5bd" stroke-width="1.5"/>
  <text x="700" y="1155" text-anchor="middle" class="subtitle">核心函数模块</text>
  <text x="70" y="1175" class="small-text">calc_vle_curve() | calc_operating_lines() | calc_q_line() | find_intersection() | rectifying_section() | stripping_section() | equilibrium_calc() | plot_mt_diagram()</text>
  
</svg>
