<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 18px; fill: #34495e; }
      .box-text-white { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 15px; fill: white; font-weight: 500; }
      .small-text { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 12px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 12px; fill: #f8f9fa; }
      .code-text { font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace; font-size: 11px; fill: white; }
      .start-box { fill: #0073e6; stroke: #005bb5; stroke-width: 1.2; }
      .input-box { fill: #4285f4; stroke: #3367d6; stroke-width: 1.2; }
      .calc-box { fill: #ea4335; stroke: #d33b2c; stroke-width: 1.2; }
      .decision-box { fill: #fbbc04; stroke: #f9ab00; stroke-width: 1.2; }
      .process-box { fill: #34a853; stroke: #2d8f47; stroke-width: 1.2; }
      .output-box { fill: #9aa0a6; stroke: #80868b; stroke-width: 1.2; }
      .graph-box { fill: #ff6d01; stroke: #e65100; stroke-width: 1.2; }
      .arrow { stroke: #3c4043; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
      .curved-arrow { stroke: #3c4043; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3c4043" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="500" y="30" text-anchor="middle" class="title">MT图解法求理论塔板数流程图</text>
  <text x="500" y="50" text-anchor="middle" class="subtitle">McCabe-Thiele Graphical Method for Theoretical Stages</text>

  <!-- 第一行：初始化流程 -->
  <!-- 开始 -->
  <rect x="50" y="80" width="140" height="60" rx="8" class="start-box"/>
  <text x="120" y="105" text-anchor="middle" class="box-text-white">开始绘制MT图</text>
  <text x="120" y="125" text-anchor="middle" class="small-text-white">Start MT Diagram</text>

  <!-- 参数输入 -->
  <rect x="220" y="80" width="140" height="60" rx="8" class="input-box"/>
  <text x="290" y="105" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="290" y="125" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>

  <!-- 建立坐标系 -->
  <rect x="390" y="80" width="140" height="60" rx="8" class="graph-box"/>
  <text x="460" y="105" text-anchor="middle" class="box-text-white">建立坐标系</text>
  <text x="460" y="125" text-anchor="middle" class="small-text-white">x-y坐标图</text>

  <!-- 绘制基础线条 -->
  <rect x="560" y="80" width="140" height="60" rx="8" class="calc-box"/>
  <text x="630" y="105" text-anchor="middle" class="box-text-white">绘制基础线条</text>
  <text x="630" y="125" text-anchor="middle" class="small-text-white">对角线+VLE线</text>

  <!-- 绘制操作线 -->
  <rect x="730" y="80" width="140" height="60" rx="8" class="calc-box"/>
  <text x="800" y="105" text-anchor="middle" class="box-text-white">绘制操作线</text>
  <text x="800" y="125" text-anchor="middle" class="small-text-white">ROL+q线+SOL</text>

  <!-- 第二行：阶梯绘制流程 -->
  <!-- 确定起始点 -->
  <rect x="50" y="180" width="140" height="60" rx="8" class="process-box"/>
  <text x="120" y="205" text-anchor="middle" class="box-text-white">确定起始点</text>
  <text x="120" y="225" text-anchor="middle" class="small-text-white">点A(xD, xD)</text>

  <!-- 阶梯绘制初始化 -->
  <rect x="220" y="180" width="140" height="60" rx="8" class="process-box"/>
  <text x="290" y="205" text-anchor="middle" class="box-text-white">阶梯绘制初始化</text>
  <text x="290" y="225" text-anchor="middle" class="small-text-white">n=0, 当前点</text>

  <!-- 垂直线到平衡线 -->
  <rect x="390" y="180" width="140" height="60" rx="8" class="graph-box"/>
  <text x="460" y="205" text-anchor="middle" class="box-text-white">垂直线到平衡线</text>
  <text x="460" y="225" text-anchor="middle" class="small-text-white">x不变，求y</text>

  <!-- 选择操作线 -->
  <rect x="560" y="180" width="140" height="60" rx="8" class="decision-box"/>
  <text x="630" y="205" text-anchor="middle" class="box-text-white">选择操作线</text>
  <text x="630" y="225" text-anchor="middle" class="small-text-white">精馏段/提馏段</text>

  <!-- 水平线到操作线 -->
  <rect x="730" y="180" width="140" height="60" rx="8" class="graph-box"/>
  <text x="800" y="205" text-anchor="middle" class="box-text-white">水平线到操作线</text>
  <text x="800" y="225" text-anchor="middle" class="small-text-white">y不变，求x</text>

  <!-- 第三行：计数和判断流程 -->
  <!-- 板数计数 -->
  <rect x="50" y="280" width="140" height="60" rx="8" class="process-box"/>
  <text x="120" y="305" text-anchor="middle" class="box-text-white">板数计数</text>
  <text x="120" y="325" text-anchor="middle" class="small-text-white">n = n + 1</text>

  <!-- 收敛判断 -->
  <rect x="220" y="280" width="140" height="60" rx="8" class="decision-box"/>
  <text x="290" y="305" text-anchor="middle" class="box-text-white">收敛判断</text>
  <text x="290" y="325" text-anchor="middle" class="small-text-white">x ≤ xW?</text>

  <!-- 结果输出 -->
  <rect x="390" y="280" width="140" height="60" rx="8" class="output-box"/>
  <text x="460" y="305" text-anchor="middle" class="box-text-white">输出结果</text>
  <text x="460" y="325" text-anchor="middle" class="small-text-white">理论塔板数</text>

  <!-- 显示MT图 -->
  <rect x="560" y="280" width="140" height="60" rx="8" class="output-box"/>
  <text x="630" y="305" text-anchor="middle" class="box-text-white">显示MT图</text>
  <text x="630" y="325" text-anchor="middle" class="small-text-white">完整阶梯图</text>

  <!-- 连接线 -->
  <!-- 第一行：水平流程 -->
  <line x1="190" y1="110" x2="220" y2="110" class="arrow"/>
  <line x1="360" y1="110" x2="390" y2="110" class="arrow"/>
  <line x1="530" y1="110" x2="560" y2="110" class="arrow"/>
  <line x1="700" y1="110" x2="730" y2="110" class="arrow"/>

  <!-- 第一行到第二行 -->
  <line x1="800" y1="140" x2="800" y2="160" class="arrow"/>
  <line x1="800" y1="160" x2="120" y2="160" class="arrow"/>
  <line x1="120" y1="160" x2="120" y2="180" class="arrow"/>

  <!-- 第二行：水平流程 -->
  <line x1="190" y1="210" x2="220" y2="210" class="arrow"/>
  <line x1="360" y1="210" x2="390" y2="210" class="arrow"/>
  <line x1="530" y1="210" x2="560" y2="210" class="arrow"/>
  <line x1="700" y1="210" x2="730" y2="210" class="arrow"/>

  <!-- 第二行到第三行 -->
  <line x1="800" y1="240" x2="800" y2="260" class="arrow"/>
  <line x1="800" y1="260" x2="120" y2="260" class="arrow"/>
  <line x1="120" y1="260" x2="120" y2="280" class="arrow"/>

  <!-- 第三行：水平流程 -->
  <line x1="190" y1="310" x2="220" y2="310" class="arrow"/>
  <line x1="360" y1="310" x2="390" y2="310" class="arrow"/>
  <line x1="530" y1="310" x2="560" y2="310" class="arrow"/>

  <!-- 循环路径：从收敛判断回到垂直线 -->
  <path d="M 290 340 Q 290 380, 50 380 Q 50 200, 390 200" class="curved-arrow"/>

  <!-- 标注 -->
  <text x="50" y="395" text-anchor="middle" class="small-text">循环：否</text>
  <text x="375" y="315" text-anchor="middle" class="small-text">是</text>

</svg>
