<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1450" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 18px; fill: #34495e; }
      .box-text-white { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 15px; fill: white; font-weight: 500; }
      .small-text { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 12px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 12px; fill: #f8f9fa; }
      .code-text { font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace; font-size: 11px; fill: white; }
      .start-box { fill: #0073e6; stroke: #005bb5; stroke-width: 1.2; }
      .input-box { fill: #4285f4; stroke: #3367d6; stroke-width: 1.2; }
      .calc-box { fill: #ea4335; stroke: #d33b2c; stroke-width: 1.2; }
      .decision-box { fill: #fbbc04; stroke: #f9ab00; stroke-width: 1.2; }
      .process-box { fill: #34a853; stroke: #2d8f47; stroke-width: 1.2; }
      .output-box { fill: #9aa0a6; stroke: #80868b; stroke-width: 1.2; }
      .graph-box { fill: #ff6d01; stroke: #e65100; stroke-width: 1.2; }
      .arrow { stroke: #3c4043; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
      .curved-arrow { stroke: #3c4043; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3c4043" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" class="title">MT图解法求理论塔板数流程图</text>
  <text x="700" y="65" text-anchor="middle" class="subtitle">McCabe-Thiele Graphical Method for Theoretical Stages</text>
  
  <!-- 开始 -->
  <rect x="600" y="90" width="200" height="60" rx="8" class="start-box"/>
  <text x="700" y="115" text-anchor="middle" class="box-text-white">开始绘制MT图</text>
  <text x="700" y="135" text-anchor="middle" class="small-text-white">Start MT Diagram</text>
  
  <!-- 参数输入 -->
  <rect x="600" y="180" width="200" height="80" rx="8" class="input-box"/>
  <text x="700" y="205" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="700" y="225" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>
  <text x="700" y="245" text-anchor="middle" class="code-text">input_parameters()</text>
  
  <!-- 建立坐标系 -->
  <rect x="600" y="290" width="200" height="80" rx="8" class="graph-box"/>
  <text x="700" y="315" text-anchor="middle" class="box-text-white">建立坐标系</text>
  <text x="700" y="335" text-anchor="middle" class="small-text-white">x-y坐标图 (0,0)到(1,1)</text>
  <text x="700" y="355" text-anchor="middle" class="code-text">setup_coordinate_system()</text>
  
  <!-- 第一行绘制模块 -->
  <!-- 绘制对角线 -->
  <rect x="120" y="420" width="180" height="80" rx="8" class="graph-box"/>
  <text x="210" y="445" text-anchor="middle" class="box-text-white">绘制对角线</text>
  <text x="210" y="465" text-anchor="middle" class="small-text-white">y = x (45°线)</text>
  <text x="210" y="485" text-anchor="middle" class="code-text">draw_diagonal_line()</text>
  
  <!-- 绘制VLE平衡线 -->
  <rect x="340" y="420" width="180" height="80" rx="8" class="calc-box"/>
  <text x="430" y="445" text-anchor="middle" class="box-text-white">绘制VLE平衡线</text>
  <text x="430" y="465" text-anchor="middle" class="small-text-white">y = αx/(1+(α-1)x)</text>
  <text x="430" y="485" text-anchor="middle" class="code-text">draw_equilibrium_curve()</text>
  
  <!-- 绘制精馏操作线 -->
  <rect x="560" y="420" width="180" height="80" rx="8" class="calc-box"/>
  <text x="650" y="445" text-anchor="middle" class="box-text-white">绘制精馏操作线</text>
  <text x="650" y="465" text-anchor="middle" class="small-text-white">ROL方程</text>
  <text x="650" y="485" text-anchor="middle" class="code-text">draw_rectifying_line()</text>
  
  <!-- 绘制q线 -->
  <rect x="780" y="420" width="180" height="80" rx="8" class="calc-box"/>
  <text x="870" y="445" text-anchor="middle" class="box-text-white">绘制q线</text>
  <text x="870" y="465" text-anchor="middle" class="small-text-white">斜率 = q/(q-1)</text>
  <text x="870" y="485" text-anchor="middle" class="code-text">draw_q_line()</text>
  
  <!-- 绘制提馏操作线 -->
  <rect x="1000" y="420" width="180" height="80" rx="8" class="calc-box"/>
  <text x="1090" y="445" text-anchor="middle" class="box-text-white">绘制提馏操作线</text>
  <text x="1090" y="465" text-anchor="middle" class="small-text-white">SOL方程</text>
  <text x="1090" y="485" text-anchor="middle" class="code-text">draw_stripping_line()</text>
  
  <!-- 确定起始点 -->
  <rect x="600" y="540" width="200" height="80" rx="8" class="process-box"/>
  <text x="700" y="565" text-anchor="middle" class="box-text-white">确定起始点</text>
  <text x="700" y="585" text-anchor="middle" class="small-text-white">点A(xD, xD)在对角线上</text>
  <text x="700" y="605" text-anchor="middle" class="code-text">set_starting_point()</text>
  
  <!-- 阶梯绘制初始化 -->
  <rect x="600" y="650" width="200" height="80" rx="8" class="process-box"/>
  <text x="700" y="675" text-anchor="middle" class="box-text-white">阶梯绘制初始化</text>
  <text x="700" y="695" text-anchor="middle" class="small-text-white">n=0, 当前点(xD, xD)</text>
  <text x="700" y="715" text-anchor="middle" class="code-text">initialize_stepping()</text>
  
  <!-- 垂直线到平衡线 -->
  <rect x="600" y="760" width="200" height="80" rx="8" class="graph-box"/>
  <text x="700" y="785" text-anchor="middle" class="box-text-white">垂直线到平衡线</text>
  <text x="700" y="805" text-anchor="middle" class="small-text-white">x不变，求y平衡值</text>
  <text x="700" y="825" text-anchor="middle" class="code-text">vertical_to_equilibrium()</text>
  
  <!-- 判断操作线选择 -->
  <polygon points="700,870 760,900 700,930 640,900" class="decision-box"/>
  <text x="700" y="895" text-anchor="middle" class="box-text-white">选择操作线</text>
  <text x="700" y="910" text-anchor="middle" class="small-text-white">精馏段/提馏段?</text>
  
  <!-- 水平线到精馏操作线 -->
  <rect x="400" y="970" width="200" height="80" rx="8" class="graph-box"/>
  <text x="500" y="995" text-anchor="middle" class="box-text-white">水平线到精馏线</text>
  <text x="500" y="1015" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="500" y="1035" text-anchor="middle" class="code-text">horizontal_to_rectifying()</text>
  
  <!-- 水平线到提馏操作线 -->
  <rect x="800" y="970" width="200" height="80" rx="8" class="graph-box"/>
  <text x="900" y="995" text-anchor="middle" class="box-text-white">水平线到提馏线</text>
  <text x="900" y="1015" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="900" y="1035" text-anchor="middle" class="code-text">horizontal_to_stripping()</text>
  
  <!-- 板数计数 -->
  <rect x="600" y="1090" width="200" height="80" rx="8" class="process-box"/>
  <text x="700" y="1115" text-anchor="middle" class="box-text-white">板数计数</text>
  <text x="700" y="1135" text-anchor="middle" class="small-text-white">n = n + 1</text>
  <text x="700" y="1155" text-anchor="middle" class="code-text">increment_stage_count()</text>
  
  <!-- 收敛判断 -->
  <polygon points="350,1090 410,1120 350,1150 290,1120" class="decision-box"/>
  <text x="350" y="1115" text-anchor="middle" class="box-text-white">x ≤ xW?</text>
  <text x="350" y="1130" text-anchor="middle" class="small-text-white">到达塔底?</text>
  
  <!-- 结果输出 -->
  <rect x="200" y="1200" width="180" height="60" rx="8" class="output-box"/>
  <text x="290" y="1225" text-anchor="middle" class="box-text-white">输出结果</text>
  <text x="290" y="1245" text-anchor="middle" class="small-text-white">理论塔板数 = n</text>
  
  <!-- 显示MT图 -->
  <rect x="420" y="1200" width="180" height="60" rx="8" class="output-box"/>
  <text x="510" y="1225" text-anchor="middle" class="box-text-white">显示MT图</text>
  <text x="510" y="1245" text-anchor="middle" class="small-text-white">完整阶梯图</text>
  
  <!-- 连接线 -->
  <!-- 主流程 -->
  <line x1="700" y1="150" x2="700" y2="180" class="arrow"/>
  <line x1="700" y1="260" x2="700" y2="290" class="arrow"/>
  
  <!-- 分散到绘制模块 -->
  <line x1="700" y1="370" x2="210" y2="420" class="arrow"/>
  <line x1="700" y1="370" x2="430" y2="420" class="arrow"/>
  <line x1="700" y1="370" x2="650" y2="420" class="arrow"/>
  <line x1="700" y1="370" x2="870" y2="420" class="arrow"/>
  <line x1="700" y1="370" x2="1090" y2="420" class="arrow"/>
  
  <!-- 汇聚到起始点 -->
  <line x1="210" y1="500" x2="650" y2="540" class="arrow"/>
  <line x1="430" y1="500" x2="675" y2="540" class="arrow"/>
  <line x1="650" y1="500" x2="700" y2="540" class="arrow"/>
  <line x1="870" y1="500" x2="725" y2="540" class="arrow"/>
  <line x1="1090" y1="500" x2="750" y2="540" class="arrow"/>
  
  <!-- 主流程继续 -->
  <line x1="700" y1="620" x2="700" y2="650" class="arrow"/>
  <line x1="700" y1="730" x2="700" y2="760" class="arrow"/>
  <line x1="700" y1="840" x2="700" y2="870" class="arrow"/>
  
  <!-- 分支到操作线 -->
  <line x1="640" y1="900" x2="600" y2="900" class="arrow"/>
  <line x1="600" y1="900" x2="500" y2="970" class="arrow"/>
  <line x1="760" y1="900" x2="800" y2="900" class="arrow"/>
  <line x1="800" y1="900" x2="900" y2="970" class="arrow"/>
  
  <!-- 汇聚到板数计数 -->
  <line x1="500" y1="1050" x2="650" y2="1090" class="arrow"/>
  <line x1="900" y1="1050" x2="750" y2="1090" class="arrow"/>
  
  <!-- 板数计数到收敛判断 -->
  <line x1="600" y1="1130" x2="410" y2="1130" class="arrow"/>
  
  <!-- 循环路径 -->
  <path d="M 290 1120 Q 250 1120, 220 1120 Q 220 950, 220 800 Q 450 800, 600 800" class="curved-arrow"/>
  
  <!-- 收敛到结果输出 -->
  <line x1="350" y1="1150" x2="350" y2="1180" class="arrow"/>
  <line x1="350" y1="1180" x2="290" y2="1200" class="arrow"/>
  <line x1="350" y1="1180" x2="510" y2="1200" class="arrow"/>
  
  <!-- 标注 -->
  <text x="570" y="915" text-anchor="middle" class="small-text">精馏段</text>
  <text x="830" y="915" text-anchor="middle" class="small-text">提馏段</text>
  <text x="250" y="1115" text-anchor="middle" class="small-text">否</text>
  <text x="370" y="1165" text-anchor="start" class="small-text">是</text>
  <text x="150" y="810" text-anchor="middle" class="small-text">循环</text>
  
</svg>
