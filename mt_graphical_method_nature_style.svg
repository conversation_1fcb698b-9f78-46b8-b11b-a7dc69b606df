<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 18px; fill: #34495e; }
      .box-text-white { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 15px; fill: white; font-weight: 500; }
      .small-text { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 12px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Arial', 'Helvetica', sans-serif; font-size: 12px; fill: #f8f9fa; }
      .code-text { font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace; font-size: 11px; fill: white; }
      .start-box { fill: #0073e6; stroke: #005bb5; stroke-width: 1.2; }
      .input-box { fill: #4285f4; stroke: #3367d6; stroke-width: 1.2; }
      .calc-box { fill: #ea4335; stroke: #d33b2c; stroke-width: 1.2; }
      .decision-box { fill: #fbbc04; stroke: #f9ab00; stroke-width: 1.2; }
      .process-box { fill: #34a853; stroke: #2d8f47; stroke-width: 1.2; }
      .output-box { fill: #9aa0a6; stroke: #80868b; stroke-width: 1.2; }
      .graph-box { fill: #ff6d01; stroke: #e65100; stroke-width: 1.2; }
      .arrow { stroke: #3c4043; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
      .curved-arrow { stroke: #3c4043; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3c4043" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" class="title">MT图解法求理论塔板数流程图</text>
  <text x="600" y="65" text-anchor="middle" class="subtitle">McCabe-Thiele Graphical Method for Theoretical Stages</text>

  <!-- 开始 -->
  <rect x="500" y="90" width="200" height="60" rx="8" class="start-box"/>
  <text x="600" y="115" text-anchor="middle" class="box-text-white">开始绘制MT图</text>
  <text x="600" y="135" text-anchor="middle" class="small-text-white">Start MT Diagram</text>

  <!-- 参数输入 -->
  <rect x="500" y="180" width="200" height="80" rx="8" class="input-box"/>
  <text x="600" y="205" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="600" y="225" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>
  <text x="600" y="245" text-anchor="middle" class="code-text">input_parameters()</text>

  <!-- 建立坐标系 -->
  <rect x="500" y="290" width="200" height="80" rx="8" class="graph-box"/>
  <text x="600" y="315" text-anchor="middle" class="box-text-white">建立坐标系</text>
  <text x="600" y="335" text-anchor="middle" class="small-text-white">x-y坐标图 (0,0)到(1,1)</text>
  <text x="600" y="355" text-anchor="middle" class="code-text">setup_coordinate_system()</text>

  <!-- 第一行绘制模块 -->
  <!-- 绘制对角线 -->
  <rect x="80" y="420" width="160" height="80" rx="8" class="graph-box"/>
  <text x="160" y="445" text-anchor="middle" class="box-text-white">绘制对角线</text>
  <text x="160" y="465" text-anchor="middle" class="small-text-white">y = x (45°线)</text>
  <text x="160" y="485" text-anchor="middle" class="code-text">draw_diagonal_line()</text>

  <!-- 绘制VLE平衡线 -->
  <rect x="260" y="420" width="160" height="80" rx="8" class="calc-box"/>
  <text x="340" y="445" text-anchor="middle" class="box-text-white">绘制VLE平衡线</text>
  <text x="340" y="465" text-anchor="middle" class="small-text-white">y = αx/(1+(α-1)x)</text>
  <text x="340" y="485" text-anchor="middle" class="code-text">draw_equilibrium_curve()</text>

  <!-- 绘制精馏操作线 -->
  <rect x="440" y="420" width="160" height="80" rx="8" class="calc-box"/>
  <text x="520" y="445" text-anchor="middle" class="box-text-white">绘制精馏操作线</text>
  <text x="520" y="465" text-anchor="middle" class="small-text-white">ROL方程</text>
  <text x="520" y="485" text-anchor="middle" class="code-text">draw_rectifying_line()</text>

  <!-- 绘制q线 -->
  <rect x="620" y="420" width="160" height="80" rx="8" class="calc-box"/>
  <text x="700" y="445" text-anchor="middle" class="box-text-white">绘制q线</text>
  <text x="700" y="465" text-anchor="middle" class="small-text-white">斜率 = q/(q-1)</text>
  <text x="700" y="485" text-anchor="middle" class="code-text">draw_q_line()</text>

  <!-- 绘制提馏操作线 -->
  <rect x="800" y="420" width="160" height="80" rx="8" class="calc-box"/>
  <text x="880" y="445" text-anchor="middle" class="box-text-white">绘制提馏操作线</text>
  <text x="880" y="465" text-anchor="middle" class="small-text-white">SOL方程</text>
  <text x="880" y="485" text-anchor="middle" class="code-text">draw_stripping_line()</text>

  <!-- 确定起始点 -->
  <rect x="500" y="540" width="200" height="80" rx="8" class="process-box"/>
  <text x="600" y="565" text-anchor="middle" class="box-text-white">确定起始点</text>
  <text x="600" y="585" text-anchor="middle" class="small-text-white">点A(xD, xD)在对角线上</text>
  <text x="600" y="605" text-anchor="middle" class="code-text">set_starting_point()</text>

  <!-- 阶梯绘制初始化 -->
  <rect x="500" y="650" width="200" height="80" rx="8" class="process-box"/>
  <text x="600" y="675" text-anchor="middle" class="box-text-white">阶梯绘制初始化</text>
  <text x="600" y="695" text-anchor="middle" class="small-text-white">n=0, 当前点(xD, xD)</text>
  <text x="600" y="715" text-anchor="middle" class="code-text">initialize_stepping()</text>

  <!-- 垂直线到平衡线 -->
  <rect x="500" y="760" width="200" height="80" rx="8" class="graph-box"/>
  <text x="600" y="785" text-anchor="middle" class="box-text-white">垂直线到平衡线</text>
  <text x="600" y="805" text-anchor="middle" class="small-text-white">x不变，求y平衡值</text>
  <text x="600" y="825" text-anchor="middle" class="code-text">vertical_to_equilibrium()</text>

  <!-- 判断操作线选择 -->
  <polygon points="600,870 660,900 600,930 540,900" class="decision-box"/>
  <text x="600" y="895" text-anchor="middle" class="box-text-white">选择操作线</text>
  <text x="600" y="910" text-anchor="middle" class="small-text-white">精馏段/提馏段?</text>

  <!-- 水平线到精馏操作线 -->
  <rect x="320" y="970" width="180" height="80" rx="8" class="graph-box"/>
  <text x="410" y="995" text-anchor="middle" class="box-text-white">水平线到精馏线</text>
  <text x="410" y="1015" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="410" y="1035" text-anchor="middle" class="code-text">horizontal_to_rectifying()</text>

  <!-- 水平线到提馏操作线 -->
  <rect x="700" y="970" width="180" height="80" rx="8" class="graph-box"/>
  <text x="790" y="995" text-anchor="middle" class="box-text-white">水平线到提馏线</text>
  <text x="790" y="1015" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="790" y="1035" text-anchor="middle" class="code-text">horizontal_to_stripping()</text>

  <!-- 板数计数 -->
  <rect x="500" y="1090" width="200" height="80" rx="8" class="process-box"/>
  <text x="600" y="1115" text-anchor="middle" class="box-text-white">板数计数</text>
  <text x="600" y="1135" text-anchor="middle" class="small-text-white">n = n + 1</text>
  <text x="600" y="1155" text-anchor="middle" class="code-text">increment_stage_count()</text>

  <!-- 收敛判断 -->
  <polygon points="300,1090 360,1120 300,1150 240,1120" class="decision-box"/>
  <text x="300" y="1115" text-anchor="middle" class="box-text-white">x ≤ xW?</text>
  <text x="300" y="1130" text-anchor="middle" class="small-text-white">到达塔底?</text>

  <!-- 结果输出 -->
  <rect x="150" y="1200" width="160" height="60" rx="8" class="output-box"/>
  <text x="230" y="1225" text-anchor="middle" class="box-text-white">输出结果</text>
  <text x="230" y="1245" text-anchor="middle" class="small-text-white">理论塔板数 = n</text>

  <!-- 显示MT图 -->
  <rect x="340" y="1200" width="160" height="60" rx="8" class="output-box"/>
  <text x="420" y="1225" text-anchor="middle" class="box-text-white">显示MT图</text>
  <text x="420" y="1245" text-anchor="middle" class="small-text-white">完整阶梯图</text>

  <!-- 连接线 -->
  <!-- 主流程 -->
  <line x1="600" y1="150" x2="600" y2="180" class="arrow"/>
  <line x1="600" y1="260" x2="600" y2="290" class="arrow"/>

  <!-- 分散到绘制模块 -->
  <line x1="600" y1="370" x2="160" y2="420" class="arrow"/>
  <line x1="600" y1="370" x2="340" y2="420" class="arrow"/>
  <line x1="600" y1="370" x2="520" y2="420" class="arrow"/>
  <line x1="600" y1="370" x2="700" y2="420" class="arrow"/>
  <line x1="600" y1="370" x2="880" y2="420" class="arrow"/>

  <!-- 汇聚到起始点 -->
  <line x1="160" y1="500" x2="550" y2="540" class="arrow"/>
  <line x1="340" y1="500" x2="575" y2="540" class="arrow"/>
  <line x1="520" y1="500" x2="600" y2="540" class="arrow"/>
  <line x1="700" y1="500" x2="625" y2="540" class="arrow"/>
  <line x1="880" y1="500" x2="650" y2="540" class="arrow"/>

  <!-- 主流程继续 -->
  <line x1="600" y1="620" x2="600" y2="650" class="arrow"/>
  <line x1="600" y1="730" x2="600" y2="760" class="arrow"/>
  <line x1="600" y1="840" x2="600" y2="870" class="arrow"/>

  <!-- 分支到操作线 -->
  <line x1="540" y1="900" x2="480" y2="900" class="arrow"/>
  <line x1="480" y1="900" x2="410" y2="970" class="arrow"/>
  <line x1="660" y1="900" x2="720" y2="900" class="arrow"/>
  <line x1="720" y1="900" x2="790" y2="970" class="arrow"/>

  <!-- 汇聚到板数计数 -->
  <line x1="410" y1="1050" x2="550" y2="1090" class="arrow"/>
  <line x1="790" y1="1050" x2="650" y2="1090" class="arrow"/>

  <!-- 板数计数到收敛判断 -->
  <line x1="500" y1="1130" x2="360" y2="1130" class="arrow"/>

  <!-- 循环路径 -->
  <path d="M 240 1120 Q 200 1120, 180 1120 Q 180 950, 180 800 Q 380 800, 500 800" class="curved-arrow"/>

  <!-- 收敛到结果输出 -->
  <line x1="300" y1="1150" x2="300" y2="1180" class="arrow"/>
  <line x1="300" y1="1180" x2="230" y2="1200" class="arrow"/>
  <line x1="300" y1="1180" x2="420" y2="1200" class="arrow"/>

  <!-- 标注 -->
  <text x="470" y="915" text-anchor="middle" class="small-text">精馏段</text>
  <text x="730" y="915" text-anchor="middle" class="small-text">提馏段</text>
  <text x="220" y="1115" text-anchor="middle" class="small-text">否</text>
  <text x="320" y="1165" text-anchor="start" class="small-text">是</text>
  <text x="120" y="810" text-anchor="middle" class="small-text">循环</text>

</svg>
