<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; fill: #34495e; }
      .box-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: #2c3e50; }
      .box-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: white; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; font-size: 14px; fill: #ecf0f1; }
      .process-box { fill: #4a90e2; stroke: #357abd; stroke-width: 1.5; }
      .rbtr-box { fill: #d73527; stroke: #b8251a; stroke-width: 1.5; }
      .decision-box { fill: #f5a623; stroke: #d4941e; stroke-width: 1.5; }
      .output-box { fill: #7ed321; stroke: #6bb518; stroke-width: 1.5; }
      .arrow { stroke: #495057; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="500" y="30" text-anchor="middle" class="title">RBTR框架流程图</text>
  <text x="500" y="55" text-anchor="middle" class="subtitle">从零散需求到结构化Prompt的转化过程</text>

  <!-- 起始：用户需求 -->
  <rect x="400" y="80" width="200" height="60" rx="10" class="process-box"/>
  <text x="500" y="105" text-anchor="middle" class="box-text-white">接收用户需求</text>
  <text x="500" y="125" text-anchor="middle" class="small-text-white">零散、非结构化信息</text>

  <!-- 箭头1 -->
  <line x1="500" y1="140" x2="500" y2="170" class="arrow"/>

  <!-- 判断：信息是否充足 -->
  <polygon points="500,170 550,200 500,230 450,200" class="decision-box"/>
  <text x="500" y="195" text-anchor="middle" class="box-text-white">信息是否</text>
  <text x="500" y="210" text-anchor="middle" class="box-text-white">充足？</text>

  <!-- 否：提问补充 -->
  <rect x="650" y="170" width="150" height="60" rx="10" class="process-box"/>
  <text x="725" y="195" text-anchor="middle" class="box-text-white">主动提问</text>
  <text x="725" y="215" text-anchor="middle" class="small-text-white">补充关键信息</text>

  <!-- 箭头：否 -->
  <line x1="550" y1="200" x2="650" y2="200" class="arrow"/>
  <text x="600" y="195" text-anchor="middle" class="small-text">否</text>

  <!-- 箭头：返回 -->
  <line x1="725" y1="170" x2="725" y2="150" class="arrow"/>
  <line x1="725" y1="150" x2="500" y2="150" class="arrow"/>

  <!-- 箭头：是 -->
  <line x1="500" y1="230" x2="500" y2="270" class="arrow"/>
  <text x="520" y="250" text-anchor="start" class="small-text">是</text>

  <!-- 信息筛选与聚焦 -->
  <rect x="400" y="270" width="200" height="60" rx="10" class="process-box"/>
  <text x="500" y="295" text-anchor="middle" class="box-text-white">信息筛选与聚焦</text>
  <text x="500" y="315" text-anchor="middle" class="small-text-white">提取核心，舍弃冗余</text>

  <!-- 箭头2 -->
  <line x1="500" y1="330" x2="500" y2="380" class="arrow"/>

  <!-- RBTR四要素 -->
  <text x="500" y="410" text-anchor="middle" class="subtitle">RBTR四要素构建</text>

  <!-- R1: Role Setting -->
  <rect x="50" y="430" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="140" y="455" text-anchor="middle" class="box-text-white">R - 角色设定</text>
  <text x="140" y="475" text-anchor="middle" class="small-text-white">Role Setting</text>
  <text x="140" y="495" text-anchor="middle" class="small-text-white">定义AI扮演的角色</text>

  <!-- B: Background -->
  <rect x="270" y="430" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="360" y="455" text-anchor="middle" class="box-text-white">B - 背景信息</text>
  <text x="360" y="475" text-anchor="middle" class="small-text-white">Background</text>
  <text x="360" y="495" text-anchor="middle" class="small-text-white">关键上下文信息</text>

  <!-- T: Task -->
  <rect x="490" y="430" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="580" y="455" text-anchor="middle" class="box-text-white">T - 任务描述</text>
  <text x="580" y="475" text-anchor="middle" class="small-text-white">Task Description</text>
  <text x="580" y="495" text-anchor="middle" class="small-text-white">清晰的核心任务</text>

  <!-- R2: Response -->
  <rect x="710" y="430" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="800" y="455" text-anchor="middle" class="box-text-white">R - 响应要求</text>
  <text x="800" y="475" text-anchor="middle" class="small-text-white">Response Requirements</text>
  <text x="800" y="495" text-anchor="middle" class="small-text-white">输出格式和要求</text>

  <!-- 连接线到RBTR -->
  <line x1="500" y1="380" x2="140" y2="430" class="arrow"/>
  <line x1="500" y1="380" x2="360" y2="430" class="arrow"/>
  <line x1="500" y1="380" x2="580" y2="430" class="arrow"/>
  <line x1="500" y1="380" x2="800" y2="430" class="arrow"/>

  <!-- 汇聚到构建Prompt -->
  <line x1="140" y1="510" x2="470" y2="550" class="arrow"/>
  <line x1="360" y1="510" x2="485" y2="550" class="arrow"/>
  <line x1="580" y1="510" x2="515" y2="550" class="arrow"/>
  <line x1="800" y1="510" x2="530" y2="550" class="arrow"/>

  <!-- 构建Prompt初稿 -->
  <rect x="400" y="550" width="200" height="60" rx="10" class="process-box"/>
  <text x="500" y="575" text-anchor="middle" class="box-text-white">构建Prompt初稿</text>
  <text x="500" y="595" text-anchor="middle" class="small-text-white">结构化整合</text>

  <!-- 箭头3 -->
  <line x1="500" y1="610" x2="500" y2="640" class="arrow"/>

  <!-- 审视初稿 -->
  <rect x="400" y="640" width="200" height="60" rx="10" class="decision-box"/>
  <text x="500" y="665" text-anchor="middle" class="box-text-white">审视初稿</text>
  <text x="500" y="685" text-anchor="middle" class="small-text-white">检查简洁性和逻辑性</text>

  <!-- 箭头4 -->
  <line x1="500" y1="700" x2="500" y2="730" class="arrow"/>

  <!-- 最终输出 -->
  <rect x="400" y="730" width="200" height="60" rx="10" class="output-box"/>
  <text x="500" y="755" text-anchor="middle" class="box-text-white">最终输出</text>
  <text x="500" y="775" text-anchor="middle" class="small-text-white">精炼的结构化Prompt</text>



</svg>
