<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: #34495e; }
      .box-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #2c3e50; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .process-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .rbtr-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .decision-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .output-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="500" y="30" text-anchor="middle" class="title">RBTR框架流程图</text>
  <text x="500" y="55" text-anchor="middle" class="subtitle">从零散需求到结构化Prompt的转化过程</text>
  
  <!-- 起始：用户需求 -->
  <rect x="400" y="80" width="200" height="60" rx="10" class="process-box"/>
  <text x="500" y="105" text-anchor="middle" class="box-text" fill="white">接收用户需求</text>
  <text x="500" y="125" text-anchor="middle" class="small-text" fill="white">零散、非结构化信息</text>
  
  <!-- 箭头1 -->
  <line x1="500" y1="140" x2="500" y2="170" class="arrow"/>
  
  <!-- 判断：信息是否充足 -->
  <polygon points="500,170 550,200 500,230 450,200" class="decision-box"/>
  <text x="500" y="195" text-anchor="middle" class="box-text" fill="white">信息是否</text>
  <text x="500" y="210" text-anchor="middle" class="box-text" fill="white">充足？</text>
  
  <!-- 否：提问补充 -->
  <rect x="650" y="170" width="150" height="60" rx="10" class="process-box"/>
  <text x="725" y="195" text-anchor="middle" class="box-text" fill="white">主动提问</text>
  <text x="725" y="215" text-anchor="middle" class="small-text" fill="white">补充关键信息</text>
  
  <!-- 箭头：否 -->
  <line x1="550" y1="200" x2="650" y2="200" class="arrow"/>
  <text x="600" y="195" text-anchor="middle" class="small-text">否</text>
  
  <!-- 箭头：返回 -->
  <line x1="725" y1="170" x2="725" y2="150" class="arrow"/>
  <line x1="725" y1="150" x2="500" y2="150" class="arrow"/>
  
  <!-- 箭头：是 -->
  <line x1="500" y1="230" x2="500" y2="270" class="arrow"/>
  <text x="520" y="250" text-anchor="start" class="small-text">是</text>
  
  <!-- 信息筛选与聚焦 -->
  <rect x="400" y="270" width="200" height="60" rx="10" class="process-box"/>
  <text x="500" y="295" text-anchor="middle" class="box-text" fill="white">信息筛选与聚焦</text>
  <text x="500" y="315" text-anchor="middle" class="small-text" fill="white">提取核心，舍弃冗余</text>
  
  <!-- 箭头2 -->
  <line x1="500" y1="330" x2="500" y2="360" class="arrow"/>
  
  <!-- RBTR四要素 -->
  <text x="500" y="380" text-anchor="middle" class="subtitle">RBTR四要素构建</text>
  
  <!-- R1: Role Setting -->
  <rect x="50" y="400" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="140" y="425" text-anchor="middle" class="box-text" fill="white">R - 角色设定</text>
  <text x="140" y="445" text-anchor="middle" class="small-text" fill="white">Role Setting</text>
  <text x="140" y="465" text-anchor="middle" class="small-text" fill="white">定义AI扮演的角色</text>
  
  <!-- B: Background -->
  <rect x="270" y="400" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="360" y="425" text-anchor="middle" class="box-text" fill="white">B - 背景信息</text>
  <text x="360" y="445" text-anchor="middle" class="small-text" fill="white">Background</text>
  <text x="360" y="465" text-anchor="middle" class="small-text" fill="white">关键上下文信息</text>
  
  <!-- T: Task -->
  <rect x="490" y="400" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="580" y="425" text-anchor="middle" class="box-text" fill="white">T - 任务描述</text>
  <text x="580" y="445" text-anchor="middle" class="small-text" fill="white">Task Description</text>
  <text x="580" y="465" text-anchor="middle" class="small-text" fill="white">清晰的核心任务</text>
  
  <!-- R2: Response -->
  <rect x="710" y="400" width="180" height="80" rx="10" class="rbtr-box"/>
  <text x="800" y="425" text-anchor="middle" class="box-text" fill="white">R - 响应要求</text>
  <text x="800" y="445" text-anchor="middle" class="small-text" fill="white">Response Requirements</text>
  <text x="800" y="465" text-anchor="middle" class="small-text" fill="white">输出格式和要求</text>
  
  <!-- 连接线到RBTR -->
  <line x1="500" y1="360" x2="140" y2="400" class="arrow"/>
  <line x1="500" y1="360" x2="360" y2="400" class="arrow"/>
  <line x1="500" y1="360" x2="580" y2="400" class="arrow"/>
  <line x1="500" y1="360" x2="800" y2="400" class="arrow"/>
  
  <!-- 汇聚到构建Prompt -->
  <line x1="140" y1="480" x2="470" y2="520" class="arrow"/>
  <line x1="360" y1="480" x2="485" y2="520" class="arrow"/>
  <line x1="580" y1="480" x2="515" y2="520" class="arrow"/>
  <line x1="800" y1="480" x2="530" y2="520" class="arrow"/>
  
  <!-- 构建Prompt初稿 -->
  <rect x="400" y="520" width="200" height="60" rx="10" class="process-box"/>
  <text x="500" y="545" text-anchor="middle" class="box-text" fill="white">构建Prompt初稿</text>
  <text x="500" y="565" text-anchor="middle" class="small-text" fill="white">结构化整合</text>
  
  <!-- 箭头3 -->
  <line x1="500" y1="580" x2="500" y2="610" class="arrow"/>
  
  <!-- 审视初稿 -->
  <rect x="400" y="610" width="200" height="60" rx="10" class="decision-box"/>
  <text x="500" y="635" text-anchor="middle" class="box-text" fill="white">审视初稿</text>
  <text x="500" y="655" text-anchor="middle" class="small-text" fill="white">检查简洁性和逻辑性</text>
  
  <!-- 箭头4 -->
  <line x1="500" y1="670" x2="500" y2="700" class="arrow"/>
  
  <!-- 最终输出 -->
  <rect x="400" y="700" width="200" height="60" rx="10" class="output-box"/>
  <text x="500" y="725" text-anchor="middle" class="box-text" fill="white">最终输出</text>
  <text x="500" y="745" text-anchor="middle" class="small-text" fill="white">精炼的结构化Prompt</text>
  
  <!-- 核心原则框 -->
  <rect x="50" y="520" width="300" height="120" rx="10" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <text x="200" y="545" text-anchor="middle" class="subtitle">核心原则</text>
  <text x="70" y="570" class="small-text">• 聚焦核心：以根本目标为导向</text>
  <text x="70" y="590" class="small-text">• 精炼简洁：去除冗余信息</text>
  <text x="70" y="610" class="small-text">• 清晰明确：使用无歧义语言</text>
  <text x="70" y="630" class="small-text">• 逻辑连贯：确保各部分逻辑清晰</text>
  
</svg>
