<?xml version="1.0" encoding="UTF-8"?>
<svg width="1300" height="750" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Nature期刊经典配色 -->
    <linearGradient id="orangeGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff7f0e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f57c00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="greenGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2ca02c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1b7e1b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="purpleGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
    </linearGradient>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto">
      <polygon points="0 0, 10 4, 0 8" fill="#2c3e50"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1300" height="750" fill="#ffffff"/>

  <!-- 第一排：核心目标 (y=100-180) -->
  <text x="650" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">机器学习探索与应用</text>
  <text x="650" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#7f8c8d">Machine Learning Exploration and Application</text>

  <!-- 第二排：实施阶段 (y=220-360) -->

  <!-- 步骤1：数据预处理 -->
  <rect x="200" y="220" width="230" height="140" rx="10" fill="#f8f9fa" stroke="#ff7f0e" stroke-width="2"/>
  <circle cx="230" cy="258" r="20" fill="url(#orangeGrad)"/>
  <text x="230" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">1</text>
  <text x="315" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">数据预处理</text>

  <!-- 数据预处理子步骤 -->
  <rect x="210" y="275" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="262" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">特征工程</text>

  <rect x="320" y="275" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="372" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">数据清洗</text>

  <rect x="210" y="315" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="262" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">数据标准化</text>

  <rect x="320" y="315" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="372" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">特征选择</text>

  <!-- 箭头1：数据预处理到模型训练 -->
  <line x1="440" y1="290" x2="460" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤2：模型训练 -->
  <rect x="470" y="220" width="230" height="140" rx="10" fill="#f8f9fa" stroke="#ff7f0e" stroke-width="2"/>
  <circle cx="500" cy="258" r="20" fill="url(#orangeGrad)"/>
  <text x="500" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">2</text>
  <text x="585" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">模型训练</text>

  <!-- 模型训练子步骤 -->
  <rect x="480" y="275" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="532" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">多算法对比</text>

  <rect x="590" y="275" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="642" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">参数调优</text>

  <rect x="480" y="315" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="532" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">集成学习</text>

  <rect x="590" y="315" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="642" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">交叉验证</text>

  <!-- 箭头2：模型训练到模型评估 -->
  <line x1="710" y1="290" x2="730" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤3：模型评估 -->
  <rect x="740" y="220" width="230" height="140" rx="10" fill="#f8f9fa" stroke="#2ca02c" stroke-width="2"/>
  <circle cx="770" cy="258" r="20" fill="url(#greenGrad)"/>
  <text x="770" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">3</text>
  <text x="855" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">模型评估</text>

  <!-- 模型评估子步骤 -->
  <rect x="750" y="275" width="105" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="802" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">性能指标</text>

  <rect x="860" y="275" width="105" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="912" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">残差分析</text>

  <rect x="750" y="315" width="105" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="802" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">学习曲线</text>

  <rect x="860" y="315" width="105" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="912" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">特征重要性</text>

  <!-- 箭头3：模型评估到模型应用 -->
  <line x1="980" y1="290" x2="1000" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤4：模型应用 -->
  <rect x="1010" y="220" width="190" height="140" rx="10" fill="#f8f9fa" stroke="#2ca02c" stroke-width="2"/>
  <circle cx="1040" cy="258" r="20" fill="url(#greenGrad)"/>
  <text x="1040" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">4</text>
  <text x="1105" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">模型应用</text>

  <!-- 模型应用子步骤 -->
  <rect x="1020" y="275" width="85" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="1062" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">预测验证</text>

  <rect x="1110" y="275" width="85" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="1152" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">结果解释</text>

  <rect x="1020" y="315" width="85" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="1062" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">工程应用</text>

  <rect x="1110" y="315" width="85" height="35" rx="4" fill="#e8f5e8" stroke="#2ca02c" stroke-width="1"/>
  <text x="1152" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">可视化</text>

  <!-- 垂直箭头：第二排到第三排 -->
  <line x1="650" y1="370" x2="650" y2="390" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第三排：技术支撑 (y=400-500) -->
  <rect x="150" y="400" width="1000" height="100" rx="10" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="650" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#9b59b6">🤖 AI智能分析辅助</text>
  <text x="650" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#9b59b6">数据分析 • 模型选择 • 参数调优 • 结果解释 • 可视化生成</text>
  <text x="650" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#9b59b6">算法推荐 • 性能优化 • 特征工程指导 • 模型解释性增强</text>

  <!-- 垂直箭头：第三排到第四排 -->
  <line x1="650" y1="510" x2="650" y2="520" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第四排：成果展示 (y=530-610) -->
  <rect x="150" y="530" width="1000" height="80" rx="10" fill="#e8f5e8" stroke="#2ca02c" stroke-width="2"/>
  <text x="650" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2ca02c">阶段成果：构建高精度预测模型，实现理论塔板数智能预测</text>
  <text x="650" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#7f8c8d">培养AI+工程的复合型人才，提升创新思维和实践能力，掌握数据科学方法</text>

</svg>
