<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; fill: #34495e; }
      .box-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: white; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #ecf0f1; }
      .code-text { font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace; font-size: 12px; fill: white; }
      .start-box { fill: #27ae60; stroke: #229954; stroke-width: 1.5; }
      .input-box { fill: #4a90e2; stroke: #357abd; stroke-width: 1.5; }
      .calc-box { fill: #d73527; stroke: #b8251a; stroke-width: 1.5; }
      .decision-box { fill: #f5a623; stroke: #d4941e; stroke-width: 1.5; }
      .process-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 1.5; }
      .output-box { fill: #1abc9c; stroke: #16a085; stroke-width: 1.5; }
      .graph-box { fill: #e67e22; stroke: #d35400; stroke-width: 1.5; }
      .arrow { stroke: #495057; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#495057" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">MT图解法求理论塔板数流程图</text>
  <text x="600" y="55" text-anchor="middle" class="subtitle">McCabe-Thiele Graphical Method for Theoretical Stages</text>
  
  <!-- 开始 -->
  <rect x="500" y="80" width="200" height="60" rx="10" class="start-box"/>
  <text x="600" y="105" text-anchor="middle" class="box-text-white">开始绘制MT图</text>
  <text x="600" y="125" text-anchor="middle" class="small-text-white">Start MT Diagram</text>
  
  <!-- 参数输入 -->
  <rect x="500" y="170" width="200" height="80" rx="10" class="input-box"/>
  <text x="600" y="195" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="600" y="215" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>
  <text x="600" y="235" text-anchor="middle" class="code-text">input_parameters()</text>
  
  <!-- 建立坐标系 -->
  <rect x="500" y="280" width="200" height="80" rx="10" class="graph-box"/>
  <text x="600" y="305" text-anchor="middle" class="box-text-white">建立坐标系</text>
  <text x="600" y="325" text-anchor="middle" class="small-text-white">x-y坐标图 (0,0)到(1,1)</text>
  <text x="600" y="345" text-anchor="middle" class="code-text">setup_coordinate_system()</text>
  
  <!-- 绘制对角线 -->
  <rect x="50" y="400" width="200" height="80" rx="10" class="graph-box"/>
  <text x="150" y="425" text-anchor="middle" class="box-text-white">绘制对角线</text>
  <text x="150" y="445" text-anchor="middle" class="small-text-white">y = x (45°线)</text>
  <text x="150" y="465" text-anchor="middle" class="code-text">draw_diagonal_line()</text>
  
  <!-- 绘制VLE平衡线 -->
  <rect x="280" y="400" width="200" height="80" rx="10" class="calc-box"/>
  <text x="380" y="425" text-anchor="middle" class="box-text-white">绘制VLE平衡线</text>
  <text x="380" y="445" text-anchor="middle" class="small-text-white">y = αx/(1+(α-1)x)</text>
  <text x="380" y="465" text-anchor="middle" class="code-text">draw_equilibrium_curve()</text>
  
  <!-- 绘制精馏操作线 -->
  <rect x="510" y="400" width="200" height="80" rx="10" class="calc-box"/>
  <text x="610" y="425" text-anchor="middle" class="box-text-white">绘制精馏操作线</text>
  <text x="610" y="445" text-anchor="middle" class="small-text-white">ROL: y = Rx/(R+1) + xD/(R+1)</text>
  <text x="610" y="465" text-anchor="middle" class="code-text">draw_rectifying_line()</text>
  
  <!-- 绘制q线 -->
  <rect x="740" y="400" width="200" height="80" rx="10" class="calc-box"/>
  <text x="840" y="425" text-anchor="middle" class="box-text-white">绘制q线</text>
  <text x="840" y="445" text-anchor="middle" class="small-text-white">斜率 = q/(q-1)</text>
  <text x="840" y="465" text-anchor="middle" class="code-text">draw_q_line()</text>
  
  <!-- 绘制提馏操作线 -->
  <rect x="970" y="400" width="200" height="80" rx="10" class="calc-box"/>
  <text x="1070" y="425" text-anchor="middle" class="box-text-white">绘制提馏操作线</text>
  <text x="1070" y="445" text-anchor="middle" class="small-text-white">SOL: 通过q线交点</text>
  <text x="1070" y="465" text-anchor="middle" class="code-text">draw_stripping_line()</text>
  
  <!-- 确定起始点 -->
  <rect x="500" y="540" width="200" height="80" rx="10" class="process-box"/>
  <text x="600" y="565" text-anchor="middle" class="box-text-white">确定起始点</text>
  <text x="600" y="585" text-anchor="middle" class="small-text-white">点A(xD, xD)在对角线上</text>
  <text x="600" y="605" text-anchor="middle" class="code-text">set_starting_point()</text>
  
  <!-- 阶梯绘制初始化 -->
  <rect x="500" y="650" width="200" height="80" rx="10" class="process-box"/>
  <text x="600" y="675" text-anchor="middle" class="box-text-white">阶梯绘制初始化</text>
  <text x="600" y="695" text-anchor="middle" class="small-text-white">n=0, 当前点(xD, xD)</text>
  <text x="600" y="715" text-anchor="middle" class="code-text">initialize_stepping()</text>
  
  <!-- 垂直线到平衡线 -->
  <rect x="250" y="780" width="200" height="80" rx="10" class="graph-box"/>
  <text x="350" y="805" text-anchor="middle" class="box-text-white">垂直线到平衡线</text>
  <text x="350" y="825" text-anchor="middle" class="small-text-white">x不变，求y平衡值</text>
  <text x="350" y="845" text-anchor="middle" class="code-text">vertical_to_equilibrium()</text>
  
  <!-- 判断操作线选择 -->
  <polygon points="600,780 660,810 600,840 540,810" class="decision-box"/>
  <text x="600" y="805" text-anchor="middle" class="box-text-white">选择操作线</text>
  <text x="600" y="820" text-anchor="middle" class="small-text-white">精馏段/提馏段?</text>
  
  <!-- 水平线到精馏操作线 -->
  <rect x="750" y="780" width="200" height="80" rx="10" class="graph-box"/>
  <text x="850" y="805" text-anchor="middle" class="box-text-white">水平线到精馏线</text>
  <text x="850" y="825" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="850" y="845" text-anchor="middle" class="code-text">horizontal_to_rectifying()</text>
  
  <!-- 水平线到提馏操作线 -->
  <rect x="750" y="920" width="200" height="80" rx="10" class="graph-box"/>
  <text x="850" y="945" text-anchor="middle" class="box-text-white">水平线到提馏线</text>
  <text x="850" y="965" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="850" y="985" text-anchor="middle" class="code-text">horizontal_to_stripping()</text>
  
  <!-- 板数计数 -->
  <rect x="500" y="1050" width="200" height="80" rx="10" class="process-box"/>
  <text x="600" y="1075" text-anchor="middle" class="box-text-white">板数计数</text>
  <text x="600" y="1095" text-anchor="middle" class="small-text-white">n = n + 1</text>
  <text x="600" y="1115" text-anchor="middle" class="code-text">increment_stage_count()</text>
  
  <!-- 收敛判断 -->
  <polygon points="300,1050 360,1080 300,1110 240,1080" class="decision-box"/>
  <text x="300" y="1075" text-anchor="middle" class="box-text-white">x ≤ xW?</text>
  <text x="300" y="1090" text-anchor="middle" class="small-text-white">到达塔底?</text>
  
  <!-- 结果输出 -->
  <rect x="450" y="1170" width="180" height="60" rx="10" class="output-box"/>
  <text x="540" y="1195" text-anchor="middle" class="box-text-white">输出结果</text>
  <text x="540" y="1215" text-anchor="middle" class="small-text-white">理论塔板数 = n</text>
  
  <!-- 显示MT图 -->
  <rect x="650" y="1170" width="180" height="60" rx="10" class="output-box"/>
  <text x="740" y="1195" text-anchor="middle" class="box-text-white">显示MT图</text>
  <text x="740" y="1215" text-anchor="middle" class="small-text-white">完整阶梯图</text>
  
  <!-- 连接线 -->
  <line x1="600" y1="140" x2="600" y2="170" class="arrow"/>
  <line x1="600" y1="250" x2="600" y2="280" class="arrow"/>
  <line x1="600" y1="360" x2="600" y2="400" class="arrow"/>
  
  <!-- 分散到各绘制模块 -->
  <line x1="600" y1="400" x2="150" y2="400" class="arrow"/>
  <line x1="600" y1="400" x2="380" y2="400" class="arrow"/>
  <line x1="600" y1="400" x2="610" y2="400" class="arrow"/>
  <line x1="600" y1="400" x2="840" y2="400" class="arrow"/>
  <line x1="600" y1="400" x2="1070" y2="400" class="arrow"/>
  
  <!-- 汇聚到起始点 -->
  <line x1="150" y1="480" x2="540" y2="540" class="arrow"/>
  <line x1="380" y1="480" x2="570" y2="540" class="arrow"/>
  <line x1="610" y1="480" x2="600" y2="540" class="arrow"/>
  <line x1="840" y1="480" x2="630" y2="540" class="arrow"/>
  <line x1="1070" y1="480" x2="660" y2="540" class="arrow"/>
  
  <line x1="600" y1="620" x2="600" y2="650" class="arrow"/>
  <line x1="600" y1="730" x2="600" y2="780" class="arrow"/>
  
  <!-- 阶梯绘制流程 -->
  <line x1="600" y1="780" x2="350" y2="780" class="arrow"/>
  <line x1="350" y1="860" x2="540" y2="810" class="arrow"/>
  <line x1="660" y1="810" x2="750" y2="810" class="arrow"/>
  <line x1="660" y1="810" x2="750" y2="950" class="arrow"/>
  
  <!-- 汇聚到板数计数 -->
  <line x1="850" y1="860" x2="650" y2="1050" class="arrow"/>
  <line x1="850" y1="1000" x2="650" y2="1050" class="arrow"/>
  
  <line x1="500" y1="1080" x2="360" y2="1080" class="arrow"/>
  <line x1="240" y1="1080" x2="200" y2="1080" class="arrow"/>
  <line x1="200" y1="1080" x2="200" y2="810" class="arrow"/>
  <line x1="200" y1="810" x2="250" y2="810" class="arrow"/>
  
  <line x1="300" y1="1110" x2="300" y2="1150" class="arrow"/>
  <line x1="300" y1="1150" x2="540" y2="1170" class="arrow"/>
  <line x1="300" y1="1150" x2="740" y2="1170" class="arrow"/>
  
  <!-- 标注 -->
  <text x="580" y="805" text-anchor="middle" class="small-text">精馏段</text>
  <text x="580" y="950" text-anchor="middle" class="small-text">提馏段</text>
  <text x="220" y="1075" text-anchor="middle" class="small-text">否</text>
  <text x="320" y="1135" text-anchor="start" class="small-text">是</text>
  
</svg>
