<?xml version="1.0" encoding="UTF-8"?>
<svg width="1300" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 20px; fill: #34495e; }
      .box-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; fill: white; }
      .small-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #5d6d7e; }
      .small-text-white { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #ecf0f1; }
      .code-text { font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace; font-size: 12px; fill: white; }
      .start-box { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .input-box { fill: #4a90e2; stroke: #357abd; stroke-width: 2; }
      .calc-box { fill: #d73527; stroke: #b8251a; stroke-width: 2; }
      .decision-box { fill: #f5a623; stroke: #d4941e; stroke-width: 2; }
      .process-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .output-box { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
      .graph-box { fill: #e67e22; stroke: #d35400; stroke-width: 2; }
      .arrow { stroke: #495057; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .line { stroke: #495057; stroke-width: 2; fill: none; }
      .curved-arrow { stroke: #495057; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#495057" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="650" y="40" text-anchor="middle" class="title">MT图解法求理论塔板数流程图</text>
  <text x="650" y="70" text-anchor="middle" class="subtitle">McCabe-Thiele Graphical Method for Theoretical Stages</text>

  <!-- 开始 -->
  <rect x="550" y="100" width="200" height="60" rx="10" class="start-box"/>
  <text x="650" y="125" text-anchor="middle" class="box-text-white">开始绘制MT图</text>
  <text x="650" y="145" text-anchor="middle" class="small-text-white">Start MT Diagram</text>

  <!-- 参数输入 -->
  <rect x="550" y="200" width="200" height="80" rx="10" class="input-box"/>
  <text x="650" y="225" text-anchor="middle" class="box-text-white">参数输入</text>
  <text x="650" y="245" text-anchor="middle" class="small-text-white">xD, xW, xF, q, R, α</text>
  <text x="650" y="265" text-anchor="middle" class="code-text">input_parameters()</text>

  <!-- 建立坐标系 -->
  <rect x="550" y="320" width="200" height="80" rx="10" class="graph-box"/>
  <text x="650" y="345" text-anchor="middle" class="box-text-white">建立坐标系</text>
  <text x="650" y="365" text-anchor="middle" class="small-text-white">x-y坐标图 (0,0)到(1,1)</text>
  <text x="650" y="385" text-anchor="middle" class="code-text">setup_coordinate_system()</text>

  <!-- 第一行绘制模块 -->
  <!-- 绘制对角线 -->
  <rect x="100" y="450" width="180" height="80" rx="10" class="graph-box"/>
  <text x="190" y="475" text-anchor="middle" class="box-text-white">绘制对角线</text>
  <text x="190" y="495" text-anchor="middle" class="small-text-white">y = x (45°线)</text>
  <text x="190" y="515" text-anchor="middle" class="code-text">draw_diagonal_line()</text>

  <!-- 绘制VLE平衡线 -->
  <rect x="320" y="450" width="180" height="80" rx="10" class="calc-box"/>
  <text x="410" y="475" text-anchor="middle" class="box-text-white">绘制VLE平衡线</text>
  <text x="410" y="495" text-anchor="middle" class="small-text-white">y = αx/(1+(α-1)x)</text>
  <text x="410" y="515" text-anchor="middle" class="code-text">draw_equilibrium_curve()</text>

  <!-- 绘制精馏操作线 -->
  <rect x="540" y="450" width="180" height="80" rx="10" class="calc-box"/>
  <text x="630" y="475" text-anchor="middle" class="box-text-white">绘制精馏操作线</text>
  <text x="630" y="495" text-anchor="middle" class="small-text-white">ROL方程</text>
  <text x="630" y="515" text-anchor="middle" class="code-text">draw_rectifying_line()</text>

  <!-- 第二行绘制模块 -->
  <!-- 绘制q线 -->
  <rect x="320" y="570" width="180" height="80" rx="10" class="calc-box"/>
  <text x="410" y="595" text-anchor="middle" class="box-text-white">绘制q线</text>
  <text x="410" y="615" text-anchor="middle" class="small-text-white">斜率 = q/(q-1)</text>
  <text x="410" y="635" text-anchor="middle" class="code-text">draw_q_line()</text>

  <!-- 绘制提馏操作线 -->
  <rect x="540" y="570" width="180" height="80" rx="10" class="calc-box"/>
  <text x="630" y="595" text-anchor="middle" class="box-text-white">绘制提馏操作线</text>
  <text x="630" y="615" text-anchor="middle" class="small-text-white">SOL方程</text>
  <text x="630" y="635" text-anchor="middle" class="code-text">draw_stripping_line()</text>

  <!-- 确定起始点 -->
  <rect x="550" y="700" width="200" height="80" rx="10" class="process-box"/>
  <text x="650" y="725" text-anchor="middle" class="box-text-white">确定起始点</text>
  <text x="650" y="745" text-anchor="middle" class="small-text-white">点A(xD, xD)在对角线上</text>
  <text x="650" y="765" text-anchor="middle" class="code-text">set_starting_point()</text>

  <!-- 阶梯绘制初始化 -->
  <rect x="550" y="820" width="200" height="80" rx="10" class="process-box"/>
  <text x="650" y="845" text-anchor="middle" class="box-text-white">阶梯绘制初始化</text>
  <text x="650" y="865" text-anchor="middle" class="small-text-white">n=0, 当前点(xD, xD)</text>
  <text x="650" y="885" text-anchor="middle" class="code-text">initialize_stepping()</text>

  <!-- 垂直线到平衡线 -->
  <rect x="550" y="940" width="200" height="80" rx="10" class="graph-box"/>
  <text x="650" y="965" text-anchor="middle" class="box-text-white">垂直线到平衡线</text>
  <text x="650" y="985" text-anchor="middle" class="small-text-white">x不变，求y平衡值</text>
  <text x="650" y="1005" text-anchor="middle" class="code-text">vertical_to_equilibrium()</text>

  <!-- 判断操作线选择 -->
  <polygon points="650,1060 710,1090 650,1120 590,1090" class="decision-box"/>
  <text x="650" y="1085" text-anchor="middle" class="box-text-white">选择操作线</text>
  <text x="650" y="1100" text-anchor="middle" class="small-text-white">精馏段/提馏段?</text>

  <!-- 水平线到精馏操作线 -->
  <rect x="350" y="1160" width="200" height="80" rx="10" class="graph-box"/>
  <text x="450" y="1185" text-anchor="middle" class="box-text-white">水平线到精馏线</text>
  <text x="450" y="1205" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="450" y="1225" text-anchor="middle" class="code-text">horizontal_to_rectifying()</text>

  <!-- 水平线到提馏操作线 -->
  <rect x="750" y="1160" width="200" height="80" rx="10" class="graph-box"/>
  <text x="850" y="1185" text-anchor="middle" class="box-text-white">水平线到提馏线</text>
  <text x="850" y="1205" text-anchor="middle" class="small-text-white">y不变，求x操作值</text>
  <text x="850" y="1225" text-anchor="middle" class="code-text">horizontal_to_stripping()</text>

  <!-- 板数计数 -->
  <rect x="550" y="1280" width="200" height="80" rx="10" class="process-box"/>
  <text x="650" y="1305" text-anchor="middle" class="box-text-white">板数计数</text>
  <text x="650" y="1325" text-anchor="middle" class="small-text-white">n = n + 1</text>
  <text x="650" y="1345" text-anchor="middle" class="code-text">increment_stage_count()</text>

  <!-- 收敛判断 -->
  <polygon points="300,1280 360,1310 300,1340 240,1310" class="decision-box"/>
  <text x="300" y="1305" text-anchor="middle" class="box-text-white">x ≤ xW?</text>
  <text x="300" y="1320" text-anchor="middle" class="small-text-white">到达塔底?</text>

  <!-- 结果输出 -->
  <rect x="100" y="1380" width="180" height="60" rx="10" class="output-box"/>
  <text x="190" y="1405" text-anchor="middle" class="box-text-white">输出结果</text>
  <text x="190" y="1425" text-anchor="middle" class="small-text-white">理论塔板数 = n</text>

  <!-- 显示MT图 -->
  <rect x="320" y="1380" width="180" height="60" rx="10" class="output-box"/>
  <text x="410" y="1405" text-anchor="middle" class="box-text-white">显示MT图</text>
  <text x="410" y="1425" text-anchor="middle" class="small-text-white">完整阶梯图</text>

  <!-- 主流程连接线 -->
  <!-- 开始到参数输入 -->
  <line x1="650" y1="160" x2="650" y2="200" class="arrow"/>

  <!-- 参数输入到建立坐标系 -->
  <line x1="650" y1="280" x2="650" y2="320" class="arrow"/>

  <!-- 建立坐标系到绘制模块 -->
  <!-- 到对角线模块 -->
  <path d="M 650 400 Q 650 410, 650 420 Q 420 420, 190 420 Q 190 435, 190 450" class="curved-arrow"/>

  <!-- 到VLE平衡线模块 -->
  <path d="M 650 400 Q 650 410, 650 420 Q 530 420, 410 420 Q 410 435, 410 450" class="curved-arrow"/>

  <!-- 到精馏操作线模块 -->
  <path d="M 650 400 Q 650 410, 650 420 Q 640 420, 630 420 Q 630 435, 630 450" class="curved-arrow"/>

  <!-- 第二行绘制模块连接 -->
  <line x1="410" y1="530" x2="410" y2="570" class="arrow"/>
  <line x1="630" y1="530" x2="630" y2="570" class="arrow"/>

  <!-- 绘制模块汇聚到起始点 -->
  <path d="M 410 650 Q 410 660, 410 670 Q 530 670, 650 670 Q 650 685, 650 700" class="curved-arrow"/>

  <!-- 起始点到初始化 -->
  <line x1="650" y1="780" x2="650" y2="820" class="arrow"/>

  <!-- 初始化到垂直线 -->
  <line x1="650" y1="900" x2="650" y2="940" class="arrow"/>

  <!-- 垂直线到判断 -->
  <line x1="650" y1="1020" x2="650" y2="1060" class="arrow"/>

  <!-- 判断到操作线选择 -->
  <!-- 到精馏操作线 -->
  <path d="M 590 1090 Q 520 1090, 450 1090 Q 450 1125, 450 1160" class="curved-arrow"/>

  <!-- 到提馏操作线 -->
  <path d="M 710 1090 Q 780 1090, 850 1090 Q 850 1125, 850 1160" class="curved-arrow"/>

  <!-- 操作线到板数计数 -->
  <!-- 精馏线到板数计数 -->
  <path d="M 450 1240 Q 450 1245, 450 1250 Q 525 1250, 600 1250 Q 600 1265, 600 1280" class="curved-arrow"/>

  <!-- 提馏线到板数计数 -->
  <path d="M 850 1240 Q 850 1255, 850 1270 Q 775 1270, 700 1270 Q 700 1275, 700 1280" class="curved-arrow"/>

  <!-- 板数计数到收敛判断 -->
  <line x1="550" y1="1320" x2="360" y2="1320" class="arrow"/>

  <!-- 收敛判断循环 -->
  <path d="M 240 1310 Q 220 1310, 200 1310 Q 200 1145, 200 980 Q 375 980, 550 980" class="curved-arrow"/>

  <!-- 收敛判断到结果输出 -->
  <!-- 到输出结果 -->
  <path d="M 300 1340 Q 300 1345, 300 1350 Q 245 1350, 190 1350 Q 190 1365, 190 1380" class="curved-arrow"/>

  <!-- 到显示MT图 -->
  <path d="M 300 1340 Q 300 1345, 300 1350 Q 355 1350, 410 1350 Q 410 1365, 410 1380" class="curved-arrow"/>

  <!-- 标注 -->
  <text x="520" y="1105" text-anchor="middle" class="small-text">精馏段</text>
  <text x="780" y="1105" text-anchor="middle" class="small-text">提馏段</text>
  <text x="180" y="1305" text-anchor="middle" class="small-text">否</text>
  <text x="320" y="1355" text-anchor="start" class="small-text">是</text>
  <text x="100" y="1000" text-anchor="middle" class="small-text">循环</text>

</svg>
