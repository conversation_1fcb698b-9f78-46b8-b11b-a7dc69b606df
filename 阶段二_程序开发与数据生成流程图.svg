<?xml version="1.0" encoding="UTF-8"?>
<svg width="1300" height="750" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Nature期刊经典配色 -->
    <linearGradient id="redGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#d62728;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c62828;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="orangeGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff7f0e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f57c00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="purpleGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
    </linearGradient>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto">
      <polygon points="0 0, 10 4, 0 8" fill="#2c3e50"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1300" height="750" fill="#ffffff"/>

  <!-- 第一排：核心目标 (y=100-180) -->
  <text x="650" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">程序开发与数据生成</text>
  <text x="650" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#7f8c8d">Program Development and Data Generation</text>

  <!-- 第二排：实施阶段 (y=220-360) -->

  <!-- 步骤1：GUI设计 -->
  <rect x="150" y="220" width="230" height="140" rx="10" fill="#f8f9fa" stroke="#d62728" stroke-width="2"/>
  <circle cx="180" cy="258" r="20" fill="url(#redGrad)"/>
  <text x="180" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">1</text>
  <text x="265" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">GUI设计</text>

  <!-- GUI设计子步骤 -->
  <rect x="160" y="275" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="212" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">界面布局</text>

  <rect x="270" y="275" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="322" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">交互设计</text>

  <rect x="160" y="315" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="212" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">参数输入</text>

  <rect x="270" y="315" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="322" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">结果显示</text>

  <!-- 箭头1：GUI设计到算法实现 -->
  <line x1="390" y1="290" x2="410" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤2：算法实现 -->
  <rect x="420" y="220" width="230" height="140" rx="10" fill="#f8f9fa" stroke="#d62728" stroke-width="2"/>
  <circle cx="450" cy="258" r="20" fill="url(#redGrad)"/>
  <text x="450" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">2</text>
  <text x="535" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">算法实现</text>

  <!-- 算法实现子步骤 -->
  <rect x="430" y="275" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="482" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">MT图解法</text>

  <rect x="540" y="275" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="592" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">数值计算</text>

  <rect x="430" y="315" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="482" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">相平衡计算</text>

  <rect x="540" y="315" width="105" height="35" rx="4" fill="#fdf2f2" stroke="#d62728" stroke-width="1"/>
  <text x="592" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">理论塔板数</text>

  <!-- 箭头2：算法实现到可视化 -->
  <line x1="660" y1="290" x2="680" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤3：可视化 -->
  <rect x="690" y="220" width="230" height="140" rx="10" fill="#f8f9fa" stroke="#ff7f0e" stroke-width="2"/>
  <circle cx="720" cy="258" r="20" fill="url(#orangeGrad)"/>
  <text x="720" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">3</text>
  <text x="805" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">可视化</text>

  <!-- 可视化子步骤 -->
  <rect x="700" y="275" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="752" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">图形绘制</text>

  <rect x="810" y="275" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="862" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">动态展示</text>

  <rect x="700" y="315" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="752" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">参数影响</text>

  <rect x="810" y="315" width="105" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="862" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">数据导出</text>

  <!-- 箭头3：可视化到数据生成 -->
  <line x1="930" y1="290" x2="950" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤4：数据生成 -->
  <rect x="960" y="220" width="190" height="140" rx="10" fill="#f8f9fa" stroke="#ff7f0e" stroke-width="2"/>
  <circle cx="990" cy="258" r="20" fill="url(#orangeGrad)"/>
  <text x="990" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">4</text>
  <text x="1055" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">数据生成</text>

  <!-- 数据生成子步骤 -->
  <rect x="970" y="275" width="85" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="1012" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">参数扫描</text>

  <rect x="1060" y="275" width="85" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="1102" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">批量计算</text>

  <rect x="970" y="315" width="85" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="1012" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">数据清洗</text>

  <rect x="1060" y="315" width="85" height="35" rx="4" fill="#fff8f0" stroke="#ff7f0e" stroke-width="1"/>
  <text x="1102" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">质量检验</text>

  <!-- 垂直箭头：第二排到第三排 -->
  <line x1="650" y1="370" x2="650" y2="390" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第三排：技术支撑 (y=400-500) -->
  <rect x="150" y="400" width="1000" height="100" rx="10" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="650" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#9b59b6">🤖 AI编程辅助</text>
  <text x="650" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#9b59b6">代码生成 • 调试支持 • 算法优化 • 界面美化 • 性能提升</text>
  <text x="650" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#9b59b6">GUI设计指导 • 数据处理优化 • 可视化增强 • 程序架构建议</text>

  <!-- 垂直箭头：第三排到第四排 -->
  <line x1="650" y1="510" x2="650" y2="520" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第四排：成果展示 (y=530-610) -->
  <rect x="150" y="530" width="1000" height="80" rx="10" fill="#e8f5e8" stroke="#2ca02c" stroke-width="2"/>
  <text x="650" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2ca02c">阶段成果：完成MT图解程序开发，生成高质量训练数据集</text>
  <text x="650" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#7f8c8d">为机器学习阶段提供可靠的数据基础和工程实践经验，培养编程思维</text>

</svg>
