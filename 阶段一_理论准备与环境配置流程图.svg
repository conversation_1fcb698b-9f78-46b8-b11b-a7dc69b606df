<?xml version="1.0" encoding="UTF-8"?>
<svg width="1300" height="750" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Nature期刊经典配色 -->
    <linearGradient id="blueGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1f77b4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565c0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lightBlueGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#87ceeb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4682b4;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="purpleGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
    </linearGradient>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto">
      <polygon points="0 0, 10 4, 0 8" fill="#2c3e50"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1300" height="750" fill="#ffffff"/>

  <!-- 第一排：核心目标 (y=100-180) -->
  <text x="650" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">理论准备与环境配置</text>
  <text x="650" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#7f8c8d">Theory Foundation and Environment Setup</text>

  <!-- 第二排：实施阶段 (y=220-360) -->

  <!-- 步骤1：理论学习 -->
  <rect x="200" y="220" width="250" height="140" rx="10" fill="#f8f9fa" stroke="#1f77b4" stroke-width="2"/>
  <circle cx="230" cy="258" r="20" fill="url(#blueGrad)"/>
  <text x="230" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">1</text>
  <text x="325" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">理论学习</text>

  <!-- 理论学习子步骤 -->
  <rect x="210" y="275" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="267" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">McCabe-Thiele法</text>

  <rect x="330" y="275" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="387" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">精馏原理复习</text>

  <rect x="210" y="315" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="267" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">相平衡关系</text>

  <rect x="330" y="315" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="387" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">操作线方程</text>

  <!-- 箭头1：理论学习到环境配置 -->
  <line x1="460" y1="290" x2="480" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤2：环境配置 -->
  <rect x="490" y="220" width="250" height="140" rx="10" fill="#f8f9fa" stroke="#1f77b4" stroke-width="2"/>
  <circle cx="520" cy="258" r="20" fill="url(#lightBlueGrad)"/>
  <text x="520" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">2</text>
  <text x="615" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">环境配置</text>

  <!-- 环境配置子步骤 -->
  <rect x="500" y="275" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="557" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">Python安装</text>

  <rect x="620" y="275" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="677" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">库环境配置</text>

  <rect x="500" y="315" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="557" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">开发环境</text>

  <rect x="620" y="315" width="115" height="35" rx="4" fill="#e8f4fd" stroke="#1f77b4" stroke-width="1"/>
  <text x="677" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">可视化工具</text>

  <!-- 箭头2：环境配置到AI工具 -->
  <line x1="750" y1="290" x2="770" y2="290" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 步骤3：AI工具熟悉 -->
  <rect x="780" y="220" width="250" height="140" rx="10" fill="#f8f9fa" stroke="#9b59b6" stroke-width="2"/>
  <circle cx="810" cy="258" r="20" fill="url(#purpleGrad)"/>
  <text x="810" y="266" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">3</text>
  <text x="905" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#2c3e50">AI工具熟悉</text>

  <!-- AI工具子步骤 -->
  <rect x="790" y="275" width="115" height="35" rx="4" fill="#f3e5f5" stroke="#9b59b6" stroke-width="1"/>
  <text x="847" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">DeepSeek使用</text>

  <rect x="910" y="275" width="115" height="35" rx="4" fill="#f3e5f5" stroke="#9b59b6" stroke-width="1"/>
  <text x="967" y="296" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">提示工程技术</text>

  <rect x="790" y="315" width="115" height="35" rx="4" fill="#f3e5f5" stroke="#9b59b6" stroke-width="1"/>
  <text x="847" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">代码生成技巧</text>

  <rect x="910" y="315" width="115" height="35" rx="4" fill="#f3e5f5" stroke="#9b59b6" stroke-width="1"/>
  <text x="967" y="336" text-anchor="middle" font-family="Arial, sans-serif" font-size="15" fill="#2c3e50">学习策略</text>

  <!-- 垂直箭头：第二排到第三排 -->
  <line x1="650" y1="370" x2="650" y2="390" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第三排：技术支撑 (y=400-500) -->
  <rect x="150" y="400" width="1000" height="100" rx="10" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="650" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#9b59b6">🤖 AI全程辅助支持</text>
  <text x="650" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#9b59b6">概念解释 • 环境配置指导 • 学习路径规划 • 问题答疑 • 代码生成 • 调试支持</text>
  <text x="650" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#9b59b6">提示工程技术 • 学习策略优化 • 个性化辅导</text>

  <!-- 垂直箭头：第三排到第四排 -->
  <line x1="650" y1="510" x2="650" y2="520" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 第四排：成果展示 (y=530-610) -->
  <rect x="150" y="530" width="1000" height="80" rx="10" fill="#e8f5e8" stroke="#2ca02c" stroke-width="2"/>
  <text x="650" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2ca02c">阶段成果：掌握理论基础，配置完成开发环境，熟练使用AI辅助工具</text>
  <text x="650" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#7f8c8d">为后续程序开发阶段奠定坚实基础，培养AI+工程的复合型思维</text>

</svg>
