生成式AI赋能化工原理精馏教学创新与实践

李致贤

（华南理工大学化学与化工学院，广东广州 510641）

 **[**  **摘要** **] **为革新化工原理精馏教学，提升学生解决复杂工程问题的能力并培养其AI素养，提出并实践了一种深度融合生成式人工智能（GAI）的教学创新模式。以二元连续精馏McCabe-Thiele（MT）图解法为例，引导学生利用以DeepSeek为代表的大语言模型（LLM）作为“智能编程伙伴”和“认知工具”，通过有效的提示工程，辅助学生自主进行Python编程，开发了兼具动态可视化与交互式参数分析功能的MT图解计算平台。在此基础上，进一步指导学生利用该平台生成的数据集，在GAI辅助下初步探索并构建了预测理论塔板数的机器学习回归模型。教学实践表明，该模式显著降低了编程的技术门槛，深化了学生对精馏过程原理的理解，有效提升了其工程计算、AI协作及数据驱动的问题解决与创新能力，并成功激发了学生的学习兴趣与探索精神。该研究为GAI技术赋能化工高等教育教学改革提供了一个具体可行的实践范例。

[关键词] 化工原理；人工智能；教学改革；精馏。

**AI-Assisted
Innovation and Practice in Teaching Principles of Chemical Engineering: A Case
Study of Design Calculation for Binary Continuous Distillation Column**

**LI Zhixian**

*(School of
Chemistry and Chemical Engineering, South China University of Technology,
Guangzhou 510641, Guangdong, China)*

**[Abstract] **To innovate
the teaching of distillation in chemical engineering principles, enhance
students' ability to solve complex engineering problems, and cultivate their AI
literacy, this study proposes and implements an innovative teaching model
deeply integrated with Generative Artificial Intelligence (GAI). Taking the
McCabe-Thiele (MT) graphical method for binary continuous distillation as an
example, students were guided to utilize Large Language Models (LLMs)
represented by DeepSeek as "intelligent programming partners" and
"cognitive tools." Through effective prompt engineering, students
were assisted in independently programming with Python to develop an
interactive MT graphical calculation platform featuring dynamic visualization
and interactive parameter analysis. Furthermore, leveraging the dataset
generated by this platform, students were guided, with GAI assistance, to
preliminarily explore and construct a machine learning regression model for
predicting theoretical plate numbers. Teaching practice demonstrates that this
model significantly lowers the technical threshold for programming, deepens
students' understanding of distillation process principles, effectively
enhances their engineering computation, AI collaboration, and data-driven
problem-solving and innovation capabilities, and successfully stimulates their
learning interest and exploratory spirit. This research provides a concrete and
feasible practical example for GAI technology empowering teaching reform in
higher chemical education.

**[Keywords]** Principles of
Chemical Engineering; Artificial Intelligence; Teaching Reform; Distillation.

化工原理是化学工程与工艺及相关专业本科教育体系中的重要专业基础课程，其核心教学目标在于培养学生应用基本原理解决复杂工程问题的能力。精馏作为核心单元操作，在工业分离中占据关键地位，其设计计算，特别是理论塔板数的确定，是教学过程中的重点与难点。传统教学方法，如逐板计算法和McCabe-Thiele（MT）图解法，在促进学生深度理解与实践应用方面存在不足：前者因计算迭代繁琐，易使学生为数值所困，难以直观洞察参数变化的影响；后者虽具直观性，但其静态呈现方式限制了动态交互与探索式学习的有效开展，学生难以深入探究参数对系统行为的细微调控机制。

近年来，以大语言模型（LLMs）为核心的生成式人工智能（Generative AI,
GAI）技术发展迅速，其在自然语言理解、代码生成、逻辑推理及知识问答等方面的能力，为工程教育革新提供了重要契机。GAI（如OpenAI的GPT系列及DeepSeek等模型）的应用，使得构建智能化、高效化与个性化学习环境成为现实。将GAI技术应用于化工原理教学，特别是在辅助学生编程实践、开发交互式计算与仿真工具方面，能有效降低编程门槛，引导学生聚焦于化工过程原理的深层理解与实际应用。同时，人机协作模式有助于培养学生的计算思维、数据分析能力及运用AI解决复杂问题的创新素养。编程实践所产生的数据亦可作为基础，引导学生初步探索机器学习（ML）等数据驱动方法，从而拓宽学术视野，提升其应对未来智能化挑战的综合能力。

鉴于此，本文聚焦于生成式AI在化工原理精馏教学中的应用模式创新，提出并实践了一种以学生为中心、项目式学习为载体、深度融合GAI辅助的教学模式。该模式以二元连续精馏MT图解法为案例，引导学生运用DeepSeek等LLM作为编程辅助与认知深化工具，通过提示工程（Prompt
Engineering）进行Python编程，自主开发交互式可视化计算平台。研究的核心目的在于：一、利用GAI的代码生成与调试支持，帮助学生将MT图解法的抽象原理转化为动态计算模型，突破传统教学方法的局限性；二、探究GAI在辅助编程教学各环节（如逻辑构建、GUI设计、算法实现、错误诊断、概念解释）的具体赋能机制及有效的人机交互策略；三、在MT图解交互程序开发完成后，引导学生利用程序生成的数据，在GAI辅助下初步掌握机器学习的基本流程，并探索其在预测理论塔板数等方面的应用潜力。本研究旨在为化工高等教育领域提供一个具体可行的GAI赋能教学改革实例，以期提升教学质量，增强学生的工程计算能力、AI协作素养及持续学习与创新能力。

一、生成式AI赋能的精馏教学创新模式设计与方法论

为将生成式AI的赋能力量有效融入化工原理精馏教学，本研究设计了一套以学生为行动主体、以项目任务为驱动、以大语言模型（LLM）为智能支撑的创新教学模式。该模式的核心是“基于McCabe-Thiele图解法的精馏塔理论塔板数交互式计算程序开发及机器学习初步应用”的综合项目，强调理论与实践的结合、编程技能与专业知识的融合、以及人机协作解决复杂问题的能力培养。

图2 教学模式总体流程

(一) 教学模式的总体框架与实施流程

教学流程设计为循序渐进的三个阶段，确保学生在GAI的辅助下，逐步构建知识、提升技能。

1. 理论准备、工具熟悉与提示工程初步。

此阶段旨在为后续项目实践奠定坚实基础。

(1) 理论与编程基础：教师系统讲授精馏操作原理、MT图解法的数学模型、计算步骤及其在工程实践中的应用。同时，为确保学生具备必要的编程技能，提供Python编程基础入门材料，内容涵盖基本语法、数据类型、控制流以及常用科学计算库（如NumPy、Matplotlib）的初步介绍。

(2) GAI工具引入与提示工程：系统介绍DeepSeek等LLM的功能特性，重点突出其在代码生成（Code
Generation）、自然语言理解、逻辑推理和问题解答方面的能力。教师详细讲授有效提示工程（Prompt
Engineering）的基本原则与技巧，例如如何清晰、准确地描述问题，如何提供必要的上下文信息，如何设定AI的角色以引导其输出，以及如何通过迭代提问优化AI的回答质量。此环节强调GAI作为辅助工具的定位，要求学生培养对AI生成内容（AIGC）的批判性思维和甄别能力，避免盲从。

2. 交互式MT图解程序开发——GAI深度赋能。

此阶段是教学模式的核心，学生在GAI辅助下，围绕精馏塔MT图解法的核心计算与可视化进行编程实践。项目要求学生开发的程序具备以下核心功能：

(1) 参数输入与物系处理：程序需提供图形用户界面（GUI），允许用户输入塔顶产品组成（x~D~）、塔底产品组成（x~W~）、进料组成（x~F~）、进料热状态参数（q）及操作回流比（R）。初步可固定物系（如苯-甲苯体系，相对挥发度α=2.5），并鼓励学生在GAI辅助下扩展至读取或处理其他物系（如乙醇-水）的汽液平衡（VLE）数据。

(2) 核心计算逻辑实现：程序内嵌基于MT图解法原理的数值计算逻辑。学生在GAI的协助下，将MT图解法的计算步骤，如汽液平衡线（VLE）、精馏段操作线（ROL）、q线及提馏段操作线（SOL）的方程建立，以及从塔顶（x~D~）开始的逐板计算直至塔底的迭代过程，转化为Python代码。GAI可辅助学生进行模块化设计（如VLE数据处理模块、操作线方程计算模块、逐板计算算法模块、q线处理模块），并针对具体模块的功能需求，通过与DeepSeek的交互获取算法思路、代码片段或完整函数。例如，学生可向AI咨询：“请基于相对挥发度α和液相组成x，编写一个Python函数计算对应的平衡气相组成y，并解释如何利用此函数绘制二元物系的汽液平衡曲线。”

(3) GUI交互界面开发：学生利用Tkinter等Python库设计和实现图形用户界面。面对GUI布局、控件使用等难点，学生可向GAI描述期望的界面元素与布局（如参数输入框、控制按钮、MT图显示区域、结果输出文本框），GAI能够生成初步的GUI框架代码，学生在此基础上进行修改和完善，实现参数的交互式输入和计算结果的动态显示。

(4) 动态可视化与阶梯绘制：程序的核心功能之一是MT图的动态绘制及理论板数阶梯的准确呈现。学生在GAI辅助下，将MT图解法的几何作图规则转化为精确的编程逻辑。例如，在不同操作条件下（特别是q线附近）操作线切换点（即加料板位置）的判断以及阶梯与平衡线、操作线交点的精确计算，是此部分的难点。GAI能够帮助学生分析和修正由于浮点数精度问题、逻辑判断边界条件疏忽等引发的绘图错误。

(5) 代码调试与优化：在编程过程中，GAI扮演“智能调试器”的角色，辅助学生理解Python的错误提示信息，定位代码缺陷（bug），并提供修复建议。学生亦可将自行编写的代码片段及遇到的问题输入给GAI，寻求代码结构或算法效率的优化方案。

(6) 结果展示：计算结果（总理论板数、精馏段板数、提馏段板数、最佳加料板位置）需在GUI界面清晰、规范地展示。

(7) 进阶功能探索：鼓励学生在AI辅助下尝试扩展程序功能，例如：读取外部数据文件（如CSV格式的VLE数据）并使用插值方法生成平衡曲线；增加物性转换模块（如体积分数与摩尔分数的转换）；自动计算并显示最小回流比等。

3. 参数敏感性分析、数据生成与机器学习初步探索——能力拓展。

此阶段旨在深化学生对精馏过程的理解，并引入数据驱动的思维方法。

(1) 交互式参数分析：学生利用自主开发的MT程序，通过在GUI界面改变回流比R、进料热状态q等关键参数，实时观察MT图的变化以及理论塔板数、加料板位置等计算结果的联动响应。这一过程有助于学生直观、深刻地理解各参数对精馏过程的具体影响。

(2) 结构化数据生成：为后续机器学习应用准备数据集。教师引导学生利用已开发的程序，系统地改变输入参数组合（如R、q、x~D~、x~W~、x~F~、α），自动运行计算并记录相应的输出结果（如理论塔板数N、进料板位置等），形成结构化的数据集。具体的数据范围和格式可参考教学辅助材料。GAI可辅助学生编写数据批量生成、格式化存储（如输出为CSV文件）的Python脚本。

(3)AI辅助的机器学习建模：

任务定义与模型选择：以精馏操作参数作为输入特征，理论塔板数N作为预测目标，构建一个简单的回归预测模型。学生可向GAI咨询适合此类任务的机器学习模型（如线性回归、决策树回归等）。

代码生成与模型训练：学生通过自然语言向GAI描述任务需求，例如：“我有一个包含R, q, x ~D~ ,
x ~F~ , x ~W~ , N等列的CSV数据集，请提供使用Python和Scikit-learn库建立一个决策树回归模型来预测N的代码，应包括数据加载、特征与标签划分、训练集与测试集分割、模型训练及均方根误差（RMSE）评估等步骤。”GAI可生成基础的ML代码框架。

结果解读与初步分析：在GAI的辅助下，学生对模型的预测结果进行初步解读，尝试将其与化工原理知识（如参数对板数影响的趋势）相联系。教师引导学生关注模型的预测精度、适用范围、潜在的过拟合风险，并鼓励其思考如何改进模型（如调整模型参数、进行特征工程、尝试其他类型的模型等）。

整个教学流程结束后，通过项目报告（含设计思路、核心代码、AI交互关键记录与反思、参数分析报告、机器学习探索报告）、程序功能演示及口头答辩等方式，对学生的学习成效进行综合评价，重点考察其知识掌握程度、编程与计算能力、AI协作水平、问题解决过程及创新意识。

(二) 生成式AI（DeepSeek）的角色深化与教师的赋能策略

为确保GAI真正赋能学生学习，防止其产生认知惰性或盲目依赖，本教学模式强调深化对AI角色的理解，并对教师的引导策略提出以下具体要求：

1. GAI作为“认知放大器”与“探索伙伴”：

(1) 自然语言驱动的编程辅助：学生主要通过自然语言与DeepSeek等LLM进行交互，将复杂的化工计算逻辑、算法思路及可视化需求，在AI的辅助下转化为具体的代码实现。这种“对话式编程”显著降低了传统编程语言的语法壁垒，使学生能够更专注于化工原理的理解和工程问题的分析与解决。

(2) 即时个性化脚手架支持：当学生在编程实践中遇到具体困难，如算法设计瓶颈、特定库函数调用疑问、代码错误排查等，DeepSeek能够提供即时的、个性化的“脚手架式”支持。学生可以通过“少量样本提示”（Few-shot
Prompting）或引导AI进行“思维链”（Chain-of-Thought）推理，来获取更符合其具体需求的解决方案或代码片段。

(3) 促进知识迁移与创新探索：GAI不仅能辅助学生完成预设的编程任务，还能根据学生的提问，拓展相关的专业知识或技术知识。例如，AI可以介绍更优化的算法、推荐功能更强大的Python库、解释代码背后的深层原理，甚至启发学生思考新的功能设计（如结合物性数据库进行更精确的汽液平衡计算，或在模型中引入塔板效率概念等），从而促进知识的迁移和创新能力的培养。

2. 教师的赋能与引导策略：

(1) 培养批判性AI素养：教师需持续引导学生对GAI生成的所有内容（AIGC），无论是代码、文字解释还是算法建议，都保持批判性审视的态度。明确指出GAI可能存在的“幻觉”（Hallucinations）现象或潜在偏见，并要求学生对关键的代码逻辑和计算结果进行严格的交叉验证（如与教材理论、经典案例计算结果进行比对）。

(2) 设计高质量的探究性学习任务：教学任务的设计应避免简单的模仿和重复，更侧重于鼓励学生进行开放式的探索和“What-if”分析。例如，在参数敏感性分析阶段，教师可以引导学生自主设计实验方案，系统探究多个操作参数（如回流比与进料热状态的组合）对精馏过程的交互影响，并利用开发的程序进行验证。

(3) 教师角色的转变：从“知识传授者”到“学习领航员”与“AI协作教练”：在GAI赋能的教学模式下，教师的主要职责从传统的知识直接灌输，转变为激发学生的学习动机、引导探究方向、教授与AI高效协作的方法与策略（如如何构建有效的Prompt序列以获取精确回答）、组织课堂研讨与经验分享，以及科学评价学生在人机协作环境下的综合能力与高阶思维发展水平。

(4) 关注高阶思维能力的培养：即使GAI能够快速提供问题的解决方案或代码，教师也应通过启发式提问，如“为什么这个方案是有效的？”、“这种算法的局限性是什么？”、“在不同条件下，这个结论是否依然成立？”、“有无其他更优的实现方式或解决方案？”等，引导学生进行深度思考和反思，确保其不仅“知其然”，更能“知其所以然”，真正内化所学知识和技能。

二、教学实施成效、具体案例与讨论

本研究提出的生成式AI赋能的化工原理精馏教学创新模式，在生物工程与生物制药专业22级（80名学生）的《传质与分离工程》课程中进行了为期一个教学模块的实践。以交互式McCabe-Thiele（MT）图解程序开发及初步的机器学习探索为核心项目，通过详细记录学生与AI（DeepSeek）的交互过程、分析项目成果（代码质量、功能完整性、报告深度）、结合课堂观察、学生访谈及匿名问卷调查，对教学成效进行了多维度评估。

(一) 交互式MT图解程序开发：AI赋能编程实践的具体体现

学生在教师的引导和AI的辅助下，普遍能够成功开发出功能完善、界面友好的MT图解计算程序。这些程序不仅实现了传统MT图解法的核心计算功能，更通过动态可视化和参数交互，显著提升了学习体验。AI在其中的赋能作用具体体现在以下几个典型场景：

1. GUI界面快速原型构建与迭代：许多初次接触Python GUI编程（如Tkinter）的学生，对布局管理、控件使用感到困惑。通过向DeepSeek提供清晰的界面需求描述，例如：“我需要一个精馏塔计算界面，左侧包含x~D~、x~W~、x~F~、q、R、α的输入框(Entry)和对应的标签(Label)，以及一个‘开始计算’按钮(Button)；右侧用Matplotlib嵌入一个绘图区域显示MT图；下方用一个文本框(Text)显示计算结果。请用Tkinter生成基础的Python代码框架。” DeepSeek能够根据这些描述生成一个可运行的初始GUI框架。学生在此基础上，可以进一步通过与AI交互，例如提出“如何将回流比R的输入框改为一个可以动态调整的滑块控件(Scale)？”或“如何在Matplotlib绘图区域中正确显示中文图例和坐标轴标签？”，从而实现界面的快速迭代和个性化定制。
2. 核心算法逻辑的辅助实现与调试：MT图解法的核心在于理论板的逐板计算，这涉及到复杂的条件判断和迭代逻辑。

(1) 操作线与q线方程的精确表达：学生在将不同进料热状态（q值）下的操作线方程和q线方程转化为Python函数时，可向AI咨询其数学表达式及代码实现方式。AI不仅能提供代码片段，还能对相关化工原理（如q线的物理意义及其对操作线的影响）进行解释，巩固学生的理论认知。

(2) 阶梯绘制的逻辑挑战与AI辅助调试：在绘制理论板阶梯时，学生常在判断从精馏段操作线切换到提馏段操作线的逻辑（即加料板位置的确定）以及阶梯与平衡线、操作线的交点精确计算上遇到困难。例如，有学生反馈其程序在q值接近1或0等特殊工况下，理论板阶梯绘制出现明显逻辑错误或图形失真。该学生将其代码片段、错误现象的详细描述以及预期的正确行为，通过精心设计的Prompt提交给DeepSeek。AI分析后，能够指出其在判断相态转换点或处理操作线切换时的逻辑缺陷，并提供修正后的代码及相应解释。这种“人机协同调试”模式显著提高了问题解决的效率和准确性。

3. 动态可视化与数据交互的实现：AI辅助学生有效运用Matplotlib库，不仅绘制出静态的MT图，更能实现图形的动态更新。例如，当用户通过GUI修改回流比R或其他操作参数后，程序能实时重绘操作线和理论板阶梯，并同步更新理论板数等计算结果。DeepSeek在此过程中帮助学生理解Matplotlib的事件处理机制（如按钮点击事件的回调函数绑定）和图形刷新方法（如canvas.draw()），从而实现计算结果与可视化的实时联动。

(二) 拓展至机器学习初步探索：AI赋能数据驱动思维培养

完成MT交互式计算程序后，学生利用该工具系统地改变操作参数组合（如回流比R、进料热状态q、进料组成xF等），生成了包含不同工况下理论塔板数N的数据集。随后，在AI辅助下进行了初步的机器学习建模探索(图2)：

![svg (1)](file:///C:/Users/<USER>/AppData/Local/Temp/msohtmlclip1/01/clip_image002.gif)

图2 机器学习实现精馏过程预测与分析

1. 数据集构建的自动化：学生首先设定各操作参数的变化范围和步长（例如，R从最小回流比的1.1倍变化到3倍，步长0.1；q值取0, 0.25, 0.5,
   0.75, 1等多个典型值）。随后，在DeepSeek的辅助下编写Python脚本，该脚本自动调用之前完成的MT计算核心函数，进行批量化计算，高效地生成了包含数百个数据点的结构化数据集，数据格式为“输入参数（x~D~ ,
   x~W~ , x ~F~ , q, R, α）- 输出结果（N）”，并自动保存为CSV文件，为后续机器学习建模奠定了基础。
2. AI辅助下的ML模型选择与实现：

(1) 自然语言驱动的模型搭建：对于初次接触机器学习的学生，他们可以直接用自然语言向DeepSeek描述其任务需求，例如：“我现在有一个名为distillation_data.csv的CSV文件，其中包含xD, xW, xF, q,
R, α作为输入特征，N作为目标输出。我想使用Python的Scikit-learn库建立一个简单的回归模型来预测N，请优先考虑决策树回归模型。请提供完整的Python代码示例，应包括：1.加载CSV数据；2.将特征和目标变量分离；3.按80:20的比例划分训练集和测试集；4.初始化并训练决策树回归模型；5.在测试集上进行预测，并计算和打印均方误差（MSE）和决定系数（R²）。” AI能够快速生成符合上述要求的、结构清晰的代码框架。

(2) 模型基本概念的即时解释：在模型构建和评估过程中，学生遇到的机器学习术语（如“特征 Feature”、“标签 Label”、“过拟合 Overfitting”、“交叉验证
Cross-validation”、“均方误差 MSE”、“决定系数 R²”等），均可随时向AI提问。AI能够像一位耐心的助教，提供清晰易懂的解释，帮助学生理解代码中各个步骤的含义和ML的基本原理。

(3) 探索不同模型与参数调优初步：在AI的帮助下，学生可以方便地修改生成的代码，尝试不同的回归模型（如从决策树回归更换为线性回归或支持向量回归），并比较它们在同一数据集上的性能表现。此外，学生还可以向AI咨询如何对特定模型进行初步的参数调整，例如：“对于刚才的决策树回归模型，如何通过调整参数来观察其对模型性能的影响？请给出修改后的代码片段。”这使得学生能够初步感知不同算法的特点和参数敏感性。

(三) 教学效果综合评估

本教学模式的初步实践表明，GAI赋能显著提升了教学成效。学生在理论理解与应用层面，通过编程实践和动态参数分析，对精馏原理及关键参数影响的认知更为深刻直观，项目报告普遍体现出理论向实践的有效转化。在计算思维与编程技能层面，即使是编程基础薄弱者，在GAI辅助下亦能完成复杂编程任务，掌握了Python及相关库的应用，并在将工程问题转化为算法模型的过程中锻炼了计算思维与逻辑能力。AI协作与创新素养层面，学生初步掌握了与GAI高效协作解决问题的方法，体验了AI在提升学习效率方面的潜力，并通过机器学习的初步探索激发了对数据科学的兴趣和创新意识。同时，新颖的教学模式显著提高了学生的学习兴趣、课堂参与度与学习主动性，营造了积极的深度学习氛围。

(四) 实践中的挑战与应对策略

尽管GAI赋能教学模式展现出显著成效，但在实践过程中仍面临若干挑战，亟需审慎应对以确保持续优化。首要挑战在于防范学生的“认知外包”风险并强化其批判性思维培养，这要求教学中必须强调AI生成内容（AIGC）的辅助定位，引导学生对AIGC进行验证、优化，并加强对其独立思考过程与AI回答合理性分析的评价，同时鼓励学生记录并反思AI交互过程。其次，针对GAI输出内容可能存在的不可靠性及“幻觉”问题，应对策略是着力培养学生的交叉验证习惯，例如将AIGC与教材理论、经典案例进行比对，鼓励单元测试和集成测试，并由教师加强对关键环节的指导与把关。再者，传统的结果导向评价体系难以全面衡量AI辅助下的学习成效，因此，构建侧重过程与高阶能力（如AI工具使用效率、提示工程质量、问题解决思路创新性、批判性思维体现）的多元评价体系至关重要，可结合项目报告、代码审查、答辩、AI交互记录及学习反思等进行综合评估。最后，此教学模式对教师的AI素养及引导能力提出了更高要求，需要通过组织专项培训以提升教师GAI技术应用和教学设计能力，鼓励跨学科交流合作，并建立支持教师持续学习与投身教学创新的长效机制，从而促进教师角色的成功重塑与专业发展。

三、结语

本研究探索并实践了生成式人工智能（GAI）赋能化工原理精馏教学的新模式。通过引导学生运用大语言模型（如DeepSeek）辅助Python编程，自主开发交互式McCabe-Thiele计算与可视化平台，并初步涉足机器学习应用，教学实践取得了积极成效。结果表明，该模式有效提升了学生对核心工程原理的深度理解、编程与工程计算能力，并初步培养了其AI协作素养与数据驱动的创新思维，同时显著激发了学习兴趣。

GAI在教学中的角色是赋能教师与学生，而非取代。它将教师从部分重复性工作中解放出来，更专注于启发与引导；将学生从繁琐的计算中解放出来，更专注于原理理解与创新实践。随着GAI技术的飞速发展，其在化工高等教育中的应用潜力巨大。未来应继续深化GAI融合层次，拓展应用广度至更多课程与环节，完善相应的教学评价体系，并加强教师AI素养的培养，以期开创化工高等教育智能化、个性化的新范式，为国家培养更多高素质创新型化工人才。

参考文献：

[[1]
孙喆](),尹晓红,范文元,张舜光,刘勇,许良华,穆曼曼.利用Python实现精馏塔逐板计算及图形化教学[J].山东化工,2021,50(21):179-181.

[[2]
辛忠](). 开启数字化赋能高等教育教学改革新征程[J].
化工高等教育,2024,41(5):1.

[[3]
吴怡逸](),罗迎春,陶文亮.化工原理课程体系教学改革实施现状、问题与对策分析[J].化工时刊,2021,35(04):47-50.

[4]
马志刚,李微,曾坤伟,等. 化工原理课程教学的改革与实践[J]. 广州化工,2019,47(9):195-196. []()

[5]
李金龙,隋国哲,张伟光,等.化工原理课程项目驱动教学改革与实践[J].高师理科学刊,2023,43(02):98-101.

[[6]
陶彩虹](), 刘宝勇,
盛丽,
雷洋,
蔡洁琼.
工程教育专业认证背景下的化工原理课程体系的建设与改革[J].
大学化学,
2021, 36(8): 2012044.

[[7]
张利鹏](),许昊翔,吴登峰,等. 基于数字孪生技术及大数据驱动的化工原理课程教学改革探索[J].
化纤与纺织技术,2024,53(2):215-217.

[[8]
李致贤](),李奇峰,刘伟峰.基于3D打印技术的离心泵教学创新与实践探索[J].化工高等教育,2024,(5):140-144.

[[9]
刘海军](),温赞玲.深度求索DeepSeek：人工智能、技术创新与新质生产力[J].当代经济管理,
2025,1-13.
