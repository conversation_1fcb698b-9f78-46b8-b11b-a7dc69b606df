目标： 将用户提供的零散、非结构化的需求，转化为一个简洁、清晰、逻辑性强且包含以下四个核心要素的结构化Prompt。在转化过程中，准确把握并保留用户的核心意图，舍弃冗余信息。如果用户信息不足以明确核心需求或框架要素，主动提问以补全。

核心原则：

聚焦核心： 始终以用户的根本目标为导向，确保最终Prompt准确反映其最核心的需求。

精炼简洁： 去除不影响核心任务理解的细节、修饰或背景噪音。

清晰明确： 使用无歧义的语言，确保AI能够准确理解每一个指令。

逻辑连贯： 确保Prompt的各个组成部分之间逻辑关系清晰，共同服务于最终目标。

Prompt框架：

角色设定 (Role Setting):

定义： 你希望AI扮演什么样的核心角色？这个角色最关键的技能、知识或身份特征是什么？

目标： 确保AI在执行核心任务时具有最关键的一致性和针对性。

如果用户未提供或不清晰，提问示例：“为了精准定位，您希望我扮演的核心角色是什么（例如：资深市场分析师、创意文案写手）？”

“这个角色需要具备哪些最关键的特质来帮助您达成目标？”

背景信息 (Background Information):

定义： 关于这个任务，有哪些最关键的上下文信息是AI必须了解才能准确执行任务的？（例如：核心目标受众、关键限制条件、最重要的项目背景）。

目标： 帮助AI理解执行核心任务所必需的最小化但关键的上下文。

如果用户未提供或不清晰，提问示例：“为了准确理解您的需求，有哪些最核心的背景信息是我必须知道的？”

“这个任务最重要的限制条件或目标受众是什么？”

任务描述 (Task Description):

定义： 清晰、简洁、具体地说明AI需要完成的核心任务。使用直接的动词和明确的期望成果。

目标： 确保AI准确理解用户的核心期望和最主要的执行动作。

如果用户未提供或不清晰，提问示例：“请用最简洁的语言告诉我，您希望我完成的核心任务是什么？”

“这个任务最主要的目标或需要产出的核心成果是什么？”

响应要求 (Response Requirements):

定义： 你对AI输出的核心内容格式、关键风格、大致长度、必须包含的要点等有什么最重要的要求？

目标： 确保AI的输出结果在形式和内容上满足用户最基本且关键的预期。

如果用户未提供或不清晰，提问示例：“关于输出结果，您有哪些必须满足的关键要求？（例如：要点列表、不超过300字、专业风格）”

“输出内容中，有哪些信息是绝对必要的，或者绝对要避免的？”

执行流程：

接收用户需求： 仔细聆听或阅读用户提供的碎片化信息。

初步理解与提问（如果需要）：快速判断用户表达的核心意图。

如果核心意图或框架要素模糊不清，优先针对这些模糊点进行提问，引导用户补充最关键的信息（参考上述提问示例）。避免过早陷入细节。

信息筛选与聚焦：在用户提供的信息（包括补充信息）中，识别并提取与核心需求直接相关的内容。

主动舍弃那些与核心任务关联不强、过于细枝末节或不影响AI理解任务主旨的冗余信息。

归类与整合： 将筛选后的核心信息对应到上述四个框架要素中，并进行简洁化处理。

构建精炼Prompt初稿：根据整合后的核心信息，草拟一个结构化的Prompt。

审视初稿： 检查是否做到了简洁、清晰、逻辑连贯，是否准确反映了用户的核心需求。自我提问：“如果我是AI，看到这个Prompt能毫不费力地理解要做什么吗？”

（可选）与用户确认核心： 如果对核心需求的把握仍有疑问，可以向用户简要复述你理解的核心任务和关键要求，以求确认。“我理解您主要是想让我[扮演角色]基于[关键背景]完成[核心任务]，并希望结果[关键响应要求]，对吗？”

最终输出： 提供经过提炼的、结构化的最终Prompt。

强调： 整个过程的关键在于识别和保留核心，剔除和简化非核心。目标是生成一个AI能够高效理解并准确执行的、最精简有效的指令集。

现在，您可以随时告诉我您的需求，我会更加注重为您构建一个简洁、清晰且直击核心的Prompt！

## 教学应用案例

### (2) GAI工具引入与提示工程

系统介绍DeepSeek等LLM的功能特性，重点突出其在代码生成（Code Generation）、自然语言理解、逻辑推理和问题解答方面的能力。教师详细讲授基于**RBTR框架的结构化提示工程**（Prompt Engineering）原则与技巧：

#### RBTR框架化提示工程的核心优势

传统的零散提示（Zero-shot prompt）和简短提示（Short prompt）往往导致AI理解偏差和执行不准确。**RBTR框架通过结构化需求表达，显著提高AI理解的准确率，促进正确执行**：

1. **角色设定（Role Setting）**：明确定义AI需要扮演的专业角色
   - 如何为不同任务设定合适的AI角色（如代码审查专家、算法设计师、技术文档编写者）
   - 角色设定对AI输出质量和专业性的影响

2. **背景信息（Background Information）**：提供关键上下文
   - 如何识别和提供任务执行所必需的技术背景
   - 上下文信息对AI理解准确性的关键作用

3. **任务描述（Task Description）**：清晰、准确地描述核心需求
   - 将模糊需求转化为具体、可执行的任务指令
   - 避免歧义表达，确保AI准确理解用户意图

4. **响应要求（Response Requirements）**：规范输出格式和质量标准
   - 设定代码风格、文档格式、解释深度等具体要求
   - 通过明确期望提升AI输出的实用性和准确性

#### 框架化提示工程的实践技巧

- **迭代优化**：通过RBTR四要素的逐步完善，持续提升提示质量
- **批判性评估**：培养学生对AI生成内容（AIGC）的甄别能力，避免盲从
- **质量控制**：建立基于RBTR框架的提示质量评估标准

此环节强调GAI作为辅助工具的定位，要求学生掌握**从零散需求到结构化Prompt的转化能力**，通过RBTR框架显著提升人机协作效率和输出质量。

---

## 教改论文完整版本

### 1. 阶段一：理论准备与环境配置

此阶段旨在为后续项目实践奠定坚实基础（图2），通过理论学习与GAI工具掌握的有机结合，培养学生的工程计算能力和AI协作技能。

#### (1) 理论与编程基础

教师系统讲授精馏操作原理、MT图解法的数学模型、计算步骤及其在工程实践中的应用。为确保学生具备必要的编程技能，同步提供Python编程基础入门材料，内容涵盖基本语法、数据类型、控制流以及常用科学计算库（如NumPy、Matplotlib）的初步介绍。理论教学与编程技能培养并重，为后续的AI辅助编程实践创造条件。

#### (2) GAI工具引入与基于RBTR框架的提示工程

系统介绍DeepSeek等大语言模型的功能特性，重点突出其在代码生成、自然语言理解、逻辑推理和问题解答方面的能力。教师详细讲授基于**RBTR框架的结构化提示工程**方法论，通过四要素系统化构建高质量Prompt。

**RBTR框架的教学实施**：传统的零散提示和简短描述往往导致AI理解偏差，影响代码生成质量。RBTR框架通过结构化需求表达，显著提高AI理解准确率和执行效果。**角色设定（Role Setting）**环节教授学生如何为AI设定专业角色，如化工计算专家或Python编程助手；**背景信息（Background Information）**部分强调提供精馏理论背景和具体工程参数的重要性；**任务描述（Task Description）**训练学生将模糊的计算需求转化为清晰、具体的编程任务；**响应要求（Response Requirements）**指导学生设定代码风格、注释规范和输出格式等具体标准。

通过RBTR框架的系统训练，学生能够掌握从零散需求到结构化Prompt的转化技能，在后续的MT图解法编程实践中实现高效的人机协作。此环节特别强调GAI作为辅助工具的定位，培养学生对AI生成内容的批判性思维和甄别能力，确保技术应用的科学性和准确性。

---

## MT图解法编程实践流程

基于RBTR框架训练后，学生将进入具体的编程实践阶段。以下流程图展示了MT图解法编程项目的完整实施过程：

![MT图解法编程实践流程图](mt_programming_flowchart.svg)

### 流程说明

该流程图展示了七个核心环节的有机结合，体现了GAI辅助下的系统化编程实践：

**前三个环节（并行开发）**：参数输入与物系处理、核心计算逻辑实现、GUI交互界面开发可以并行进行，学生可以根据个人特长和兴趣选择不同的切入点。

**中间三个环节（迭代优化）**：动态可视化与阶梯绘制、代码调试与优化、结果展示形成迭代循环，通过不断的测试和改进提升程序质量。

**最终环节（功能扩展）**：进阶功能探索鼓励学生在掌握基础功能后，利用GAI辅助探索更复杂的工程应用。

整个流程充分体现了RBTR框架在实际编程项目中的应用价值，学生通过结构化的提示工程与AI进行高效协作，实现从理论学习到工程实践的有效转化。

---

## MT图解法代码逻辑流程

在掌握了项目实施流程后，学生需要深入理解MT图解法的具体算法实现。以下流程图展示了代码层面的详细逻辑：

![MT图解法代码逻辑流程图](mt_algorithm_flowchart.svg)

### 算法逻辑说明

该代码逻辑流程图展示了MT图解法的完整算法实现过程：

**初始化阶段**：程序启动后进行参数输入和验证，确保输入数据的有效性。

**计算准备阶段**：并行计算VLE数据、操作线方程、q线方程、交点位置和加料板位置，为后续逐板计算做准备。

**逐板计算核心算法**：
- 从塔顶开始，初始化计算参数
- 在精馏段使用精馏操作线（ROL）进行计算
- 判断是否到达加料板位置
- 切换到提馏段使用提馏操作线（SOL）继续计算
- 每一步都进行VLE平衡计算
- 检查收敛条件（x ≤ xW）
- 未收敛则板数递增，继续循环

**结果输出阶段**：计算完成后输出总理论板数，并绘制MT图显示阶梯过程。

### RBTR框架在代码开发中的应用

学生可以运用RBTR框架与GAI协作开发各个核心函数：

**角色设定**：化工计算专家、Python算法工程师
**背景信息**：MT图解法原理、精馏塔操作参数、VLE关系
**任务描述**：实现特定函数功能（如calc_vle_curve()、find_intersection()等）
**响应要求**：代码规范、注释完整、错误处理、测试用例

通过这种结构化的协作方式，学生能够高效地实现复杂的化工计算算法。

## RBTR框架流程图

以下是RBTR框架的可视化流程图，展示了从用户需求到结构化Prompt的完整转化过程。

![RBTR框架流程图](rbtr_framework_flowchart.svg)

### 流程说明

1. **接收用户需求**：收集零散、非结构化的用户信息
2. **信息充足性判断**：评估是否有足够信息构建完整Prompt
3. **主动提问**（如需要）：针对模糊点进行有针对性的提问
4. **信息筛选与聚焦**：提取核心信息，舍弃冗余内容
5. **RBTR四要素构建**：
   - **R - 角色设定**：定义AI需要扮演的角色
   - **B - 背景信息**：提供必要的上下文
   - **T - 任务描述**：明确核心任务
   - **R - 响应要求**：规定输出格式和要求
6. **构建Prompt初稿**：将四要素整合为结构化Prompt
7. **审视初稿**：检查简洁性、清晰性和逻辑连贯性
8. **最终输出**：提供精炼的结构化Prompt

### 核心原则贯穿始终

- **聚焦核心**：始终以用户的根本目标为导向
- **精炼简洁**：去除不影响核心任务理解的细节
- **清晰明确**：使用无歧义的语言
- **逻辑连贯**：确保各组成部分逻辑关系清晰
