# 阶段二：精馏塔计算程序与数据生成项目指南

## 1. 项目概述与目标

在完成了"阶段一：Python环境安装与配置指南"中的准备工作后，我们现在进入项目的核心部分。本项目旨在指导您通过结合化工原理中的 McCabe-Thiele 法、Python 编程实践以及现代 AI 工具 (如 Deepseek)，来深入理解和计算苯-甲苯二元体系的精馏过程。核心任务是开发一个名为 `DistillationSimulator.py` 的Python程序，该程序能够根据用户输入的工艺参数计算精馏塔所需的理论塔板数，并具备一个基础的图形化界面 (GUI) 进行交互和结果展示。

更进一步，项目要求您基于开发的程序，分析关键操作参数（如回流比 R 和进料热状况 q）对理论塔板数的影响。您通过此项目开发的计算工具及其生成的数据，将直接服务于"阶段三：动手学机器学习：预测精馏塔理论塔板数"中的模型训练和数据分析任务，为构建机器学习预测模型奠定坚实的数据基础和过程理解。

## 2. 作业任务要求详解

以下是本次综合作业的具体任务和要求：

### 一、作业任务要求：

本次作业以苯-甲苯二元体系为例，塔顶为全凝器（泡点回流），塔底为再沸器，体系相对挥发度取平均值 α=2.5，进行以下任务：

*   根据已知的塔顶产品组成（xD）、塔底产品组成（xW）、进料组成（xF）及进料热状况参数（q），开发Python程序并搭建图形化界面，计算精馏塔的理论塔板数。
*   重点考察：
    *   回流比（R）
    *   进料热状况参数（q）
    对理论塔板数的影响，开展深入分析与讨论。

### 二、输出内容：

学生提交的内容应包括以下各部分：

**（一）文献调研（15%）**

*   利用CNKI、中国知网、Web of Science等数据库，调研精馏过程理论塔板数的相关文献（至少3篇）。
*   撰写文献调研综述，总结理论塔板数计算方法、回流比和进料状况影响等关键研究成果。

**（二）理论基础与方法介绍（15%）**

*   介绍理论塔板数计算的基本原理：
    *   相对挥发度（α）的定义及其在精馏过程中的作用（取α=2.5）
    *   精馏段、提馏段操作线方程
    *   回流比R、进料热状况参数（q=0、0.5、1）对精馏的影响分析

**（三）Deepseek交互与提示词优化（20%）**

*   利用Deepseek工具辅助程序开发，记录并分析交互过程。
*   提示词（Prompt）优化过程的详细记录与优化效果分析（提供对话截图及过程描述）。

### 三、程序设计与实现（30%）

**（一）基本要求（占20%）：**

*   设计并编写Python程序：
    *   用户输入参数：
        *   塔顶组成（xD）、塔底组成（xW）、进料组成（xF）、进料热状况参数（q）、回流比（R）
        *   固定参数：相对挥发度 α = 2.5
    *   程序输出：
        *   总理论塔板数（精馏段与提馏段）
        *   加料板位置
*   图形化界面，便于用户友好地输入参数并展示输出结果。

**输出结果示例：**

```
计算结果：
- 总理论塔板数：N = 10
- 加料位置：第5块板
- 精馏段塔板数：4块
- 提馏段塔板数：6块（含再沸器）
```

**（二）进阶要求（占10%）：**

*   实现McCabe-Thiele（MT）法图形界面演示：
    *   清晰绘制相平衡曲线（α=2.5）
    *   精馏段、提馏段操作线及进料线（q线）图示
    *   直观展示逐板计算的阶梯过程，体现理论塔板数及各板组成
    *   用户界面友好交互，便于直观理解精馏计算过程

### 四、结果分析与讨论（20%）

*   针对以下情况进行详细分析：
    *   在不同回流比（建议至少3个不同回流比）的条件下，考察理论塔板数变化趋势
    *   在不同进料热状况参数（q=0、0.5、1，即饱和蒸汽、混合相、饱和液体进料）条件下，分析理论塔板数的变化趋势及理论原因
*   结合理论和已有文献深入讨论影响机制及规律。

### 五、综合报告提交格式：

*   提交综合报告PDF文件，报告结构如下：
    `苯-甲苯精馏塔板数综合作业_学号_姓名.pdf`
    ```
    ├─ 封面（题目、学生姓名、学号、班级、日期）
    ├─ 目录
    ├─ 摘要（中英文，300字以内）
    ├─ 第1章 引言（背景、目的、文献综述）
    ├─ 第2章 理论基础（相对挥发度、相平衡、q线及操作线理论）
    ├─ 第3章 Deepseek交互与提示词优化（过程截图、优化描述）
    │  ├─ 程序设计流程图
    │  ├─ Python关键代码及说明
    │  └─ 图形化界面介绍（含MT法图解展示）
    ├─ 第4章 程序设计与实现（基本功能与进阶MT法界面）
    │  ├─ 程序设计流程图
    │  ├─ Python关键代码及说明
    │  └─ 图形化界面介绍（含MT法图解展示）
    ├─ 第5章 结果分析与讨论（回流比、q参数对塔板数影响）
    ├─ 第6章 结论与展望
    ├─ 参考文献（至少8篇，英文不少于3篇）
    └─ 附录（完整程序代码、界面截图与运行说明）
    ```

## 3. Python 程序 (DistillationSimulator.py 核心) 设计与实现指南

本节为程序设计部分提供一些指导和建议。

### (1) 核心计算逻辑 (McCabe-Thiele 法)

程序的核心是实现 McCabe-Thiele 法来计算理论塔板数。关键步骤包括：

1.  **相平衡方程 (VLE)**:
    对于理想二元体系，气相组成 y 和液相组成 x 的关系为：
    \[ y = \frac{\alpha x}{1 + (\alpha - 1)x} \]
    其中 α 为相对挥发度 (固定为 2.5)。

2.  **精馏段操作线方程**:
    \[ y_{n+1} = \frac{R}{R+1}x_n + \frac{x_D}{R+1} \]
    其中 R 是回流比，xD 是塔顶产品组成。

3.  **提馏段操作线方程**:
    \[ y_{m+1} = \frac{L'}{V'}x_m - \frac{W x_W}{V'} = \frac{R_{min}+1+q-1}{R_{min}+1+q} x_m - \frac{x_W}{R_{min}+1+q} \]
    (注意: 提馏段操作线的具体形式取决于 q 值和物料衡算，通常表达为与 xW 相关)
    更通用的形式是：
    \[ y_m = \frac{\bar{L}}{\bar{V}} x_{m+1} - \frac{W x_W}{\bar{V}} \]
    其中 \(\bar{L}\) 和 \(\bar{V}\) 是提馏段的液相和气相摩尔流量。

4.  **q 线方程 (进料线方程)**:
    当 q ≠ 1 (非饱和液体进料) 时:
    \[ y = \frac{q}{q-1}x - \frac{x_F}{q-1} \]
    当 q = 1 (饱和液体进料) 时，q 线为垂直线 x = xF。
    当 q = 0 (饱和蒸汽进料) 时，q 线为水平线 y = xF。
    q 线的交点与精馏段、提馏段操作线相交于一点，或与相平衡线相交。

5.  **逐板计算**:
    *   **精馏段**: 从塔顶开始 (x_D)，利用精馏段操作线和相平衡线交替计算，直到液相组成 x ≤ x_feed_plate (进料板液相组成，通常是 q 线与操作线的交点对应的液相组成，或者是刚好跨过 xF 的点)。
    *   **提馏段**: 从塔底开始 (x_W)，利用提馏段操作线和相平衡线交替计算，直到液相组成 x ≥ x_feed_plate。
    *   注意处理全凝器和再沸器，它们通常各算一块理论塔板。

### (2) 输入与输出参数

*   **输入**:
    *   `xD` (塔顶易挥发组分摩尔分数)
    *   `xW` (塔底易挥发组分摩尔分数)
    *   `xF` (进料易挥发组分摩尔分数)
    *   `q` (进料热状况参数: 0-饱和蒸汽, 0.5-汽液混合, 1-饱和液体)
    *   `R` (回流比)
    *   `alpha` (相对挥发度, 固定为 2.5)
*   **输出**:
    *   `N_total` (总理论塔板数，含再沸器)
    *   `N_feed_plate` (加料板位置，从塔顶数下，第1块为凝器下一块板)
    *   `N_rectifying` (精馏段理论塔板数)
    *   `N_stripping` (提馏段理论塔板数，含再沸器)

### (3) 图形用户界面 (GUI)

*   可以使用 Python 的 GUI 库，如:
    *   `Tkinter`: Python 内置库，简单易上手。
    *   `PyQt` 或 `PySide`: 功能更强大，界面更美观，但学习曲线稍陡。
    *   `Kivy`: 适合创建具有新颖用户界面的应用程序。
*   界面应包含输入框供用户输入上述参数，以及显示输出结果的区域。
*   **进阶要求**: McCabe-Thiele 图的绘制。
    *   使用 `Matplotlib` 库进行绘图。
    *   绘制 x-y 相平衡曲线。
    *   绘制精馏段操作线、提馏段操作线和 q 线。
    *   在图上清晰地绘制出阶梯，直观显示理论塔板的计算过程。

### (4) 程序结构建议

*   将核心计算逻辑 (McCabe-Thiele 法计算) 封装成独立的函数或类。
*   GUI 部分调用这些核心函数进行计算和结果展示。
*   确保代码模块化，易于理解和维护。

## 4. `DistillationSimulator.py` (核心计算模块) 与机器学习的联系

完成上述精馏计算程序 (`DistillationSimulator.py`) 后，其核心计算模块 (我们暂且称之为 `calculate_theoretical_plates`) 将是您在"阶段三：动手学机器学习：预测精馏塔理论塔板数"中生成训练数据集的关键工具。

**如何生成数据集:**

1.  **定义参数范围**: 参考阶段三中 `generate_dataset.py` 的思路。
    *   回流比 `R`: 例如，从最小回流比 (Rmin) 的 1.1 倍到 3 倍，选取多个点。
    *   进料组成 `xF`: 例如，从 0.2 到 0.8，选取多个点。
    *   进料热状况 `q`: 0, 0.25, 0.5, 0.75, 1 (或更多)。
    *   塔顶组成 `xD` 和塔底组成 `xW` 可以根据项目需求固定，或也作为变量。

2.  **批量运行计算**:
    您将需要编写一个辅助脚本 (在阶段三中会具体实现为 `generate_dataset.py`)，该脚本会循环遍历上述参数的不同组合。对于每一种组合，调用 `DistillationSimulator.py` 中的 `calculate_theoretical_plates` 函数，获取计算得到的总理论塔板数 `N_total`、加料板位置 `N_feed_plate` 等。

3.  **存储数据**:
    将每次计算的输入参数 (R, xF, q, xD, xW) 和输出结果 (N_total, N_feed_plate) 保存到一个结构化的文件中，例如 CSV 文件 (`distillation_data_generated.csv`)。每一行代表一个工况下的计算结果。

    示例 CSV 文件 (此处的 `distillation_data_generated.csv` 将作为阶段三的输入):
    ```csv
    R,xF,q,xD,xW,N_total,N_feed_plate
    1.5,0.5,1.0,0.95,0.05,10,5
    1.6,0.5,1.0,0.95,0.05,9,5
    ... (更多数据点)
    ```

**数据集的用途 (承接阶段三的机器学习):**

*   **模型训练**: 使用生成的数据集训练机器学习模型 (如回归模型)，用以预测在给定操作条件下 (R, xF, q, xD, xW) 的理论塔板数 `N_total`。
*   **参数敏感性分析**: 分析不同输入参数对理论塔板数的影响程度。
*   **过程优化**: 结合模型进行操作参数的优化，以达到期望的分离效果或最低能耗。
*   **替代模型**: 构建一个快速响应的替代模型，替代复杂的逐板计算，用于在线估算或大规模模拟。

因此，本阶段开发的 `DistillationSimulator.py` 是连接精馏过程第一性原理计算和后续数据驱动机器学习方法的关键桥梁，直接为阶段三提供核心数据源。

**关于 `DistillationSimulator.py` 与阶段三数据生成脚本 (如 `Dist_data.py` 或 `generate_dataset.py`) 的关系**：

值得注意的是，在阶段三的机器学习流程中，用于批量生成数据集的脚本（例如，在阶段三教程中可能提到的 `Dist_data.py` 或 `generate_dataset.py`），其核心的精馏计算逻辑来源于本阶段您开发的 `DistillationSimulator.py`。可以将用于数据生成的脚本视为 `DistillationSimulator.py` 的一个精简版或调用其核心计算模块的版本。它通常会剥离 `DistillationSimulator.py` 中的图形用户界面（GUI）及其他与批量数据生成不直接相关的功能，仅保留或调用核心的理论塔板数计算函数（例如 `calculate_theoretical_plates`），以便更高效、更专注于自动化地为机器学习模型生成训练所需的各种工况数据。

## 5. 评分标准（总分100分）

| 评分项目             | 评分要点                                   | 分值比例 |
| -------------------- | ------------------------------------------ | -------- |
| 文献调研             | 综述质量、文献数量与合理性                 | 15%      |
| 理论基础             | 基础理论清晰准确，逻辑严谨                 | 15%      |
| Deepseek辅助         | 交互记录完整、提示词优化效果清晰体现       | 20%      |
| 程序实现（基础）     | 程序运行稳定，结果准确，界面友好           | 20%      |
| 程序实现（进阶MT法） | MT法图解清晰、动态交互展示直观性好         | 10%      |
| 结果分析与讨论       | 分析深入合理，图表呈现清晰，讨论透彻       | 20%      |

## 6. 结语与展望

本指南旨在帮助您顺利完成本次涉及精馏计算程序开发的综合项目。通过本阶段的学习与实践，您不仅将掌握 McCabe-Thiele 法的编程实现，还将为即将到来的"阶段三：动手学机器学习：预测精馏塔理论塔板数"准备好核心的模拟工具和数据生成能力。在实践过程中，请积极利用 Deepseek 等工具辅助开发，并深入理解理论知识。预祝您项目顺利，并在结合传统化工计算与现代数据科学方法的探索中取得收获！ 