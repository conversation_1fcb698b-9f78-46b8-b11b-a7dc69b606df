# SVG图表设计规范与要求

## 基本设计原则
- 使用专业的学术期刊配色方案
- 保持清晰的层次结构
- 确保在各种尺寸下的可读性
- 遵循现代简约设计理念
- 避免信息重复，突出核心内容
- 采用递进式逻辑结构

## 技术规范
- 文件格式：SVG (可缩放矢量图形)
- 编码：UTF-8
- 兼容性：支持主流浏览器和学术期刊要求
- 画布尺寸：推荐1300×750px (适合大屏展示)
- 背景：纯白色 (#ffffff)

## 配色方案
采用Nature期刊经典配色：
- 主蓝色：#1f77b4 (理论/基础)
- 主红色：#d62728 (实践/开发)
- 主橙色：#ff7f0e (应用/分析)
- 主绿色：#2ca02c (成果/评估)
- 主紫色：#9b59b6 (AI/技术)
- 文字色：#2c3e50 (主要文字)
- 辅助色：#7f8c8d (说明文字)

## 字体规范
### 层次化字体大小
- **主标题**：24px，加粗 (font-weight: bold)
- **副标题**：20px，常规
- **区域标题**：22px，加粗
- **阶段标题**：19px，加粗
- **能力模块**：16px，加粗
- **子步骤**：13px，常规
- **描述文字**：14px，常规
- **AI赋能说明**：15px，加粗
- **成效项目**：17px，加粗
- **总结文字**：16px，常规

### 字体族
- 统一使用：Arial, sans-serif
- 确保跨平台兼容性

## 布局要求
### 画布与边距
- **画布尺寸**：1300×750px (宽敞显示)
- **左右边距**：150px (23%比例)
- **内容区域**：1000px宽度
- **垂直间距**：30-40px标准间距

### 层次结构 (四层布局)
1. **第一排**：核心目标 (y=100-180, 高度80px)
2. **第二排**：实施阶段 (y=220-360, 高度140px)
3. **第三排**：技术支撑 (y=400-500, 高度100px)
4. **第四排**：成果展示 (y=530-610, 高度80px)

### 对齐要求
- **水平对齐**：所有主要元素居中对齐 (x=650)
- **垂直对齐**：同层元素基准线统一
- **数字标题对齐**：圆圈数字与标题文字在同一水平线 (y=258)
- **像素级精确**：避免1px的偏差

## 框架设计规范
### 主框架
- **圆角半径**：10-12px
- **边框宽度**：2px
- **内边距**：15-25px
- **阴影效果**：可选，保持简洁

### 子框架
- **圆角半径**：4-6px
- **边框宽度**：1px
- **高度标准**：32px (子步骤框)
- **宽度适配**：根据内容调整，保持视觉平衡

### 圆圈数字
- **半径**：20px
- **字体大小**：20px，加粗
- **颜色**：白色文字，渐变背景
- **位置**：与标题文字水平对齐

## 间距标准
### 垂直间距
- **标题间距**：25px (主副标题)
- **区域间距**：30-40px (标准间距)
- **内容间距**：15px (同区域内元素)
- **行间距**：15px (多行文字)

### 水平间距
- **元素间距**：10px (子步骤框间)
- **模块间距**：20px (能力模块间)
- **阶段间距**：50px (主要阶段间)

## 箭头设计
### 样式规范
- **线宽**：3px
- **颜色**：#2c3e50
- **长度**：20px (简洁连接)
- **箭头头部**：三角形，5px高度

### 连接规则
- **水平连接**：用于阶段间的逻辑流向
- **垂直连接**：用于层次间的支撑关系
- **精确对齐**：箭头指向目标元素中心

## 文字处理
### 内容原则
- **简洁明了**：避免冗余描述
- **层次清晰**：标题→内容→说明的层次
- **信息密度**：适中，不拥挤不空旷

### 排版规则
- **居中对齐**：text-anchor="middle"
- **基线对齐**：同层元素统一基线
- **换行处理**：长文本适当分行
- **标点规范**：使用中文标点

## 颜色语义
### 功能色彩
- **蓝色系**：理论、基础、概念
- **红色系**：实践、开发、实施
- **橙色系**：应用、分析、处理
- **绿色系**：成果、评估、效果
- **紫色系**：AI技术、创新、赋能

### 渐变效果
- **圆圈背景**：使用linearGradient
- **方向**：水平渐变 (x1="0%" x2="100%")
- **色彩过渡**：同色系深浅变化

## 响应式设计
### 缩放适配
- **矢量特性**：SVG天然支持无损缩放
- **字体大小**：相对单位，保持比例
- **最小尺寸**：确保在小屏幕上可读

### 设备兼容
- **桌面显示**：1300×750px最佳
- **投影展示**：支持4K分辨率
- **打印输出**：300DPI高质量
- **移动设备**：自适应缩放

## 质量控制
### 检查清单
- [ ] 所有文字清晰可读
- [ ] 颜色对比度符合标准
- [ ] 元素对齐精确无误
- [ ] 间距统一规范
- [ ] 信息层次清晰
- [ ] 无内容重复
- [ ] 无元素超出边界
- [ ] 箭头指向准确

### 输出标准
- **文件大小**：控制在500KB以内
- **代码整洁**：结构清晰，注释完整
- **兼容性**：主流浏览器测试通过
- **可维护性**：便于后续修改调整

## 常见问题解决
### 对齐问题
- 使用统一的基准线
- 检查x, y坐标的精确性
- 避免奇数像素造成的模糊

### 字体问题
- 统一字体族设置
- 检查字体大小层次
- 确保跨平台显示一致

### 颜色问题
- 使用标准色值
- 检查对比度
- 考虑色盲用户需求

### 布局问题
- 保持合理的信息密度
- 确保充足的呼吸空间
- 维护视觉平衡

## 版本控制
- **文件命名**：使用描述性名称
- **版本标记**：在注释中记录版本信息
- **变更记录**：记录主要修改内容
- **备份策略**：保留关键版本备份

## 实践经验总结

### 布局重构最佳实践
1. **逻辑优先原则**：先确定信息层次，再设计视觉布局
   - 目标层 → 实施层 → 支撑层 → 成果层
   - 避免平铺式布局，采用层次化结构

2. **标题简化原则**：
   - 删除冗余前缀（如"阶段一："）
   - 直接描述功能内容
   - 利用数字圆圈标识序号

3. **信息去重原则**：
   - 避免在多个位置重复相同信息
   - 集中展示核心概念
   - 底部标识移至顶部标题

### 对齐精度要求
1. **像素级精确对齐**：
   - 圆圈数字与标题文字必须在同一水平线
   - 使用统一的y坐标基准线 (如y=258)
   - 检查所有元素的x, y坐标精确性

2. **基准线系统**：
   - 建立清晰的网格系统
   - 所有元素对齐到网格点
   - 避免随意的位置调整

### 间距标准化
1. **垂直间距规律**：
   - 主要区域间：30-40px
   - 同区域内元素：15px
   - 标题行间距：25px

2. **水平间距规律**：
   - 子步骤框间：10px
   - 主要模块间：50px
   - 左右边距：150px (23%比例)

### 字体大小优化策略
1. **渐进式增大**：
   - 从小字体开始，逐步增大
   - 保持相对比例关系
   - 确保层次清晰

2. **可读性测试**：
   - 在不同设备上测试
   - 考虑观看距离
   - 确保最小字体不低于12px

### 框架超出问题解决
1. **边界检查**：
   - 计算所有子元素的右边界
   - 确保不超出主框架范围
   - 预留5-10px安全边距

2. **动态调整策略**：
   - 优先调整框架宽度
   - 其次调整元素间距
   - 最后调整元素尺寸

### 颜色语义化应用
1. **功能色彩映射**：
   - 蓝色：理论基础 (#1f77b4)
   - 红色：实践开发 (#d62728)
   - 橙色：应用分析 (#ff7f0e)
   - 绿色：成果评估 (#2ca02c)
   - 紫色：AI技术 (#9b59b6)

2. **渐变效果使用**：
   - 圆圈背景使用渐变增强立体感
   - 同色系深浅变化
   - 避免过度使用渐变

### 箭头设计优化
1. **长度控制**：
   - 标准长度20px
   - 避免过长造成视觉干扰
   - 确保指向精确

2. **位置对齐**：
   - 箭头指向目标元素中心
   - 与连接元素的中心线对齐
   - 保持水平或垂直方向

3. **复杂流程箭头处理**：
   - **避免覆盖框图**：重新设计连接线路径，避免箭头直接穿越其他元素
   - **分段连接**：将长距离连接改为分段连接，使用中间转折点
   - **循环路径优化**：对于循环流程，使用清晰的回路设计
   - **标注配合**：在关键转折点添加文字标注（如"循环"、"是/否"）
   - **层次化连接**：不同类型的连接使用不同的路径策略
     - 并行分散：从一点分散到多个目标
     - 汇聚收集：从多个源点汇聚到一个目标
     - 循环回路：形成闭合的处理循环

### 响应式考虑
1. **画布尺寸选择**：
   - 1300×750px适合大屏展示
   - 保持16:9或4:3比例
   - 考虑打印输出需求

2. **缩放适配**：
   - SVG矢量特性保证无损缩放
   - 字体大小保持相对比例
   - 最小显示尺寸测试

### 质量检查流程
1. **视觉检查**：
   - 所有元素对齐检查
   - 字体大小层次检查
   - 颜色对比度检查
   - 间距统一性检查

2. **功能检查**：
   - 信息层次逻辑检查
   - 内容完整性检查
   - 无重复信息检查
   - 箭头指向准确性检查

3. **兼容性检查**：
   - 多浏览器显示测试
   - 不同分辨率测试
   - 打印输出测试

### 常见错误避免
1. **对齐错误**：
   - 避免手动调整位置
   - 使用计算得出的精确坐标
   - 建立标准化的位置参数

2. **间距不一致**：
   - 建立间距标准
   - 使用统一的间距值
   - 避免随意调整

3. **字体混乱**：
   - 统一字体族设置
   - 建立字体大小层次
   - 避免过多字体变化

4. **颜色滥用**：
   - 遵循既定配色方案
   - 保持颜色语义一致
   - 避免过多颜色使用

### 维护更新策略
1. **版本管理**：
   - 使用描述性文件名
   - 记录主要变更内容
   - 保留关键版本备份

2. **文档同步**：
   - 及时更新设计规范
   - 记录新的最佳实践
   - 分享经验教训

3. **标准化流程**：
   - 建立检查清单
   - 制定审核流程
   - 持续优化改进

## MT图解法流程图设计案例

### 项目背景
MT图解法求理论塔板数流程图是化工教学中的关键技术流程图，涉及复杂的循环逻辑和多分支判断，对箭头设计提出了很高要求。

### 核心挑战
1. **复杂循环流程**：阶梯绘制过程需要形成清晰的循环回路
2. **多分支判断**：精馏段/提馏段的操作线选择
3. **避免视觉混乱**：多条连接线可能造成交叉和覆盖
4. **保持逻辑清晰**：在复杂连接中保持流程的可读性

### 解决方案实践

#### 1. 循环路径设计
**问题**：从"板数计数"回到"垂直线到平衡线"的循环路径容易与其他元素重叠

**解决方案**：
```svg
<!-- 优化前：直接连接，容易覆盖其他框架 -->
<line x1="425" y1="910" x2="425" y2="680" class="arrow"/>

<!-- 优化后：分段连接，避免覆盖 -->
<line x1="350" y1="860" x2="350" y2="900" class="arrow"/>
<line x1="350" y1="900" x2="540" y2="900" class="arrow"/>
<line x1="540" y1="900" x2="540" y2="810" class="arrow"/>
```

**关键技巧**：
- 使用中间转折点避免直接穿越
- 在空白区域设置水平/垂直连接段
- 添加"循环"标注明确回路逻辑

#### 2. 分支判断优化
**问题**：精馏段/提馏段的分支选择需要清晰的视觉区分

**解决方案**：
- 使用不同的转折路径区分两个分支
- 在分支点添加明确的标注（"精馏段"/"提馏段"）
- 保持分支路径的对称性和美观性

#### 3. 汇聚连接处理
**问题**：多个绘制模块汇聚到起始点时连接线密集

**解决方案**：
```svg
<!-- 从不同角度汇聚，避免重叠 -->
<line x1="150" y1="480" x2="540" y2="540" class="arrow"/>
<line x1="380" y1="480" x2="570" y2="540" class="arrow"/>
<line x1="610" y1="480" x2="600" y2="540" class="arrow"/>
<line x1="840" y1="480" x2="630" y2="540" class="arrow"/>
<line x1="1070" y1="480" x2="660" y2="540" class="arrow"/>
```

**设计原则**：
- 计算精确的汇聚角度
- 确保每条线都有清晰的指向
- 避免线条在汇聚点重叠

#### 4. 文字与箭头协调
**问题**：长文本可能与箭头路径冲突

**解决方案**：
- 简化框内文字："ROL: y = Rx/(R+1) + xD/(R+1)" → "ROL方程"
- 调整标注位置：将"精馏段/提馏段"标注移到不干扰箭头的位置
- 使用数学符号：如"ROL ∩ q-line"替代"ROL intersect q-line"

### 设计成果
1. **视觉清晰**：所有连接线都有明确的路径，无重叠覆盖
2. **逻辑明确**：循环和分支流程一目了然
3. **专业美观**：保持了学术图表的专业外观
4. **易于理解**：学生能够清晰理解MT图解法的完整流程

### 经验总结
1. **预先规划**：在设计复杂流程图前，先绘制草图规划连接路径
2. **分层设计**：将连接线分为不同层次，避免交叉
3. **标注辅助**：适当的文字标注能够大大提高流程图的可读性
4. **迭代优化**：复杂流程图需要多次迭代才能达到最佳效果

### 技术要点
- **坐标计算精确**：所有转折点都需要精确计算坐标
- **路径规划合理**：优先使用水平/垂直连接，避免斜线交叉
- **视觉层次清晰**：主要流程线与辅助连接线要有视觉区分
- **响应式考虑**：确保在不同尺寸下连接关系依然清晰

---

*本规范基于实际项目经验总结，适用于学术期刊、会议演示和教学展示等场景。*
*最后更新：2024年12月，包含精馏教学流程图设计的完整经验和MT图解法复杂箭头处理案例。*