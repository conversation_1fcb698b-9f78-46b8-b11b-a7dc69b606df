<svg width="1300" height="750" xmlns="http://www.w3.org/2000/svg">
 <defs>
  <linearGradient y2="0%" x2="100%" y1="0%" x1="0%" id="svg_3">
   <stop stop-color="#1f77b4" offset="0%"/>
   <stop stop-color="#1565c0" offset="100%"/>
  </linearGradient>
  <linearGradient y2="0%" x2="100%" y1="0%" x1="0%" id="svg_2">
   <stop stop-color="#d62728" offset="0%"/>
   <stop stop-color="#c62828" offset="100%"/>
  </linearGradient>
  <linearGradient y2="0%" x2="100%" y1="0%" x1="0%" id="svg_1">
   <stop stop-color="#ff7f0e" offset="0%"/>
   <stop stop-color="#f57c00" offset="100%"/>
  </linearGradient>
 </defs>
 <g>
  <title>Layer 1</title>
  <defs transform="translate(0 100) translate(0 134) translate(14 0) translate(-64 44) translate(147 0) translate(-1 0) translate(104 22) translate(-250 0) translate(0 -75)">
   <!-- Nature期刊经典配色 -->
   <!-- 箭头标记 -->
  </defs>
  <!-- 背景 -->
  <!-- 标题 -->
  <!-- 核心能力培养目标 - 第一排 -->
  <!-- 能力模块 -->
  <!-- 阶段一：理论准备与环境配置 - 第二排 -->
  <!-- 阶段一子步骤 -->
  <!-- 箭头1 -->
  <!-- 阶段二：程序开发与数据生成 - 第二排 -->
  <!-- 阶段二子步骤 -->
  <!-- 箭头2 -->
  <!-- 阶段三：机器学习探索与应用 - 第二排 -->
  <!-- 阶段三子步骤 -->
  <!-- 生成式AI全程赋能核心区域 - 第三排 -->
  <!-- AI技术应用 -->
  <!-- 教学成效展示 - 第四排 -->
  <rect stroke="null" x="154.46715" y="58.64032" fill="#ffffff" height="625.56812" width="1046.84551"/>
  <text fill="#2c3e50" font-weight="bold" font-size="24" font-family="Arial, sans-serif" text-anchor="middle" y="106.49919" x="688.61679">基于生成式AI的精馏教学创新模式 - 三阶段递进式培养体系</text>
  <text fill="#7f8c8d" font-size="20" font-family="Arial, sans-serif" text-anchor="middle" y="131.49919" x="688.61679">Theory Foundation → Program Development → Machine Learning Application</text>
  <rect stroke-width="2" stroke="#1f77b4" fill="#f0f8ff" rx="10" height="80" width="1000" y="166.49919" x="188.61679"/>
  <text fill="#1f77b4" font-weight="bold" font-size="22" font-family="Arial, sans-serif" text-anchor="middle" y="191.49919" x="688.61679">核心能力培养目标</text>
  <rect stroke="#1f77b4" fill="#e8f4fd" rx="5" height="32" width="230" y="206.49919" x="213.61679"/>
  <text fill="#2c3e50" font-weight="bold" font-size="16" font-family="Arial, sans-serif" text-anchor="middle" y="225.49919" x="328.61679">工程计算能力</text>
  <rect stroke="#d62728" fill="#fdf2f2" rx="5" height="32" width="230" y="206.49919" x="463.61679"/>
  <text fill="#2c3e50" font-weight="bold" font-size="16" font-family="Arial, sans-serif" text-anchor="middle" y="225.49919" x="578.61679">编程实践能力</text>
  <rect stroke="#ff7f0e" fill="#fff8f0" rx="5" height="32" width="230" y="206.49919" x="713.61679"/>
  <text fill="#2c3e50" font-weight="bold" font-size="16" font-family="Arial, sans-serif" text-anchor="middle" y="225.49919" x="828.61679">数据分析能力</text>
  <rect stroke="#9b59b6" fill="#f3e5f5" rx="5" height="32" width="200" y="206.49919" x="963.61679"/>
  <text fill="#2c3e50" font-weight="bold" font-size="16" font-family="Arial, sans-serif" text-anchor="middle" y="225.49919" x="1063.61679">AI协作素养</text>
  <rect stroke-width="2" stroke="#1f77b4" fill="#f8f9fa" rx="10" height="140" width="300" y="288.49919" x="188.61679"/>
  <circle fill="url(#svg_3)" r="20" cy="316.49919" cx="228.61679"/>
  <text fill="white" font-weight="bold" font-size="20" font-family="Arial, sans-serif" text-anchor="middle" y="324.49919" x="228.61679">1</text>
  <text fill="#2c3e50" font-weight="bold" font-size="19" font-family="Arial, sans-serif" text-anchor="middle" y="323.49919" x="348.61679">理论准备与环境配置</text>
  <rect stroke="#1f77b4" fill="#e8f4fd" rx="4" height="32" width="80" y="351.49919" x="203.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="243.61679">理论学习</text>
  <rect stroke="#1f77b4" fill="#e8f4fd" rx="4" height="32" width="80" y="351.49919" x="293.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="333.61679">环境配置</text>
  <rect stroke="#9b59b6" fill="#f3e5f5" rx="4" height="32" width="80" y="351.49919" x="383.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="423.61679">AI工具熟悉</text>
  <text fill="#7f8c8d" font-size="14" font-family="Arial, sans-serif" text-anchor="middle" y="400.49919" x="335.61679">McCabe-Thiele理论 • Python环境</text>
  <text fill="#7f8c8d" font-size="14" font-family="Arial, sans-serif" text-anchor="middle" y="418.49919" x="333.61679">DeepSeek使用 • 提示工程技术</text>
  <polyline fill="none" stroke-width="3" stroke="#2c3e50" points="498.6163489818573,367.49981689453125 508.6163489818573,367.49981689453125 508.6163489818573,367.49981689453125 518.6163794994354,367.49981689453125 "/>
  <polygon fill="#2c3e50" points="518.6163794994354,362.49981689453125 528.6163794994354,367.49981689453125 518.6163794994354,372.49981689453125 "/>
  <rect stroke-width="2" stroke="#d62728" fill="#f8f9fa" rx="10" height="140" width="300" y="286.49919" x="537.61679"/>
  <circle fill="url(#svg_2)" r="20" cy="316.49919" cx="578.61679"/>
  <text fill="white" font-weight="bold" font-size="20" font-family="Arial, sans-serif" text-anchor="middle" y="324.49919" x="578.61679">2</text>
  <text fill="#2c3e50" font-weight="bold" font-size="19" font-family="Arial, sans-serif" text-anchor="middle" y="323.49919" x="698.61679">程序开发与数据生成</text>
  <rect stroke="#d62728" fill="#fdf2f2" rx="4" height="32" width="65" y="351.49919" x="553.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="585.61679">GUI设计</text>
  <rect stroke="#d62728" fill="#fdf2f2" rx="4" height="32" width="65" y="351.49919" x="628.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="660.61679">算法实现</text>
  <rect stroke="#ff7f0e" fill="#fff8f0" rx="4" height="32" width="65" y="351.49919" x="703.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="735.61679">可视化</text>
  <rect stroke="#ff7f0e" fill="#fff8f0" rx="4" height="32" width="55" y="351.49919" x="778.61679"/>
  <text fill="#2c3e50" font-size="13" font-family="Arial, sans-serif" text-anchor="middle" y="370.49919" x="805.61679">数据生成</text>
  <text fill="#7f8c8d" font-size="14" font-family="Arial, sans-serif" text-anchor="middle" y="401.49919" x="693.61679">MT图解程序 • 核心算法 • 参数分析</text>
  <text fill="#7f8c8d" font-size="14" font-family="Arial, sans-serif" text-anchor="middle" y="418.49919" x="694.61679">GUI界面 • 可视化展示 • 数据集生成</text>
  <polyline fill="none" stroke-width="3" stroke="#2c3e50" points="848.6163794994354,367.49981689453125 858.6163794994354,367.49981689453125 858.6163794994354,367.49981689453125 868.6163794994354,367.49981689453125 "/>
  <polygon fill="#2c3e50" points="868.6163794994354,362.49981689453125 878.6163794994354,367.49981689453125 868.6163794994354,372.49981689453125 "/>
  <rect stroke-width="2" stroke="#ff7f0e" fill="#f8f9fa" rx="10" height="140" width="300" y="286.49919" x="888.61679"/>
  <circle fill="url(#svg_1)" r="20" cy="316.49919" cx="928.61679"/>
  <text fill="white" font-weight="bold" font-size="20" font-family="Arial, sans-serif" text-anchor="middle" y="324.49919" x="928.61679">3</text>
  <text fill="#2c3e50" font-weight="bold" font-size="19" font-family="Arial, sans-serif"


---

*Your access expires in 4 days. [Purchase a subscription](https://app.augmentcode.com/account)*